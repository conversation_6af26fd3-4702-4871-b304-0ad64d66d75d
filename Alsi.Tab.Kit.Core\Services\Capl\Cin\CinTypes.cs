﻿using System;

namespace Alsi.Tab.Kit.Core.Services.Capl.Cin
{
    public static class CinTypes
    {
        //诊断支持的服务结构体
        public class STRU_SID_SUBID
        {
            public byte sessionMask;
            public byte sid;
            public byte subIDcnt;
            public byte[] subID = Array.Empty<byte>();
        };

        //22服务支持的DID结构体
        public class STRU_DID
        {
            public byte sessionMask;

            [CinProp(isHex: true)]
            public int did;
            public int valueLength;

            [CinProp(isHex: true)]
            public byte[] value = Array.Empty<byte>();

            public static STRU_DID FromCaplParam(Capl.STRU_DID struDid)
            {
                return new STRU_DID
                {
                    sessionMask = struDid.sessionMask,
                    did = (int)struDid.did,
                    value = struDid.value,
                    valueLength = struDid.valueLength
                };
            }
        };

        //31服务Parameter的数据结构体
        public class STRU_routine
        {
            [CinProp(isHex: true)]
            public byte subId;
            public int reqLength;
            public int resLength;
            public byte[] reqBytes = Array.Empty<byte>();
        };

        //31服务支持的RID结构体
        public class STRU_RID
        {
            public byte sessionMask;
            [CinProp(isHex: true)]
            public int rid;
            public byte routineType;
            public byte[] securityLevels = Array.Empty<byte>();
            public STRU_routine[] struRoutineList = Array.Empty<STRU_routine>();
        };

        //2F服务IOID的Parameter结构体
        public class STRU_ioControlParameter
        {
            public int ioControlParameter;
            public int reqLength;
            public int resLength;
            public byte[] reqBytes = Array.Empty<byte>();
        };

        //2F服务支持的IOID结构体
        public class STRU_IOID
        {
            [CinProp(isHex: true)]
            public int ioid;
            public STRU_ioControlParameter[] struIOcontrolParamList = Array.Empty<STRU_ioControlParameter>();
        };

        //仿真报文帧结构体
        public class FrameFormat
        {
            [CinProp(isHex: true)]
            public int frameID;
            public byte cycle;
            public byte dataLen;
        };

        //仿真信号结构体
        public class E2EsignalList
        {
            public int startBit;
            public byte length;
            public byte sigType;

        };

        //仿真报文结构体
        public class E2EFrame
        {
            public FrameFormat frameFormat;
            public int dataId;
            public E2EsignalList[] signalList = Array.Empty<E2EsignalList>();
        };

        //19服务snapshot对应DID结构体
        public class STRU_SNAPSHOT_DID
        {
            [CinProp(isHex: true)]
            public int did;
            public byte didLen;
        };

        //19服务snapshot结构体
        public class SNAPSHOT_INFO
        {
            [CinProp(isHex: true)]
            public byte snapshotID;
            public byte snapshotNum;
            public STRU_SNAPSHOT_DID[] snapshotdidList = Array.Empty<STRU_SNAPSHOT_DID>();
        };

        //19服务Extended结构体
        public class STRU_DTC_EXTDATA
        {
            [CinProp(isHex: true)]
            public byte ExtDataRecordNum;
            public byte ExtDataLen;

            public static STRU_DTC_EXTDATA FromCaplParam(Capl.STRU_DTC_EXTDATA dtcExtData)
            {
                return new STRU_DTC_EXTDATA
                {
                    ExtDataLen = dtcExtData.extDataLen,
                    ExtDataRecordNum = dtcExtData.extDataRecordNum
                };
            }
        };
    }
}
