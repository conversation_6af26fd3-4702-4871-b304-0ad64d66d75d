{"version": 3, "file": "js/app.c7f98529.js", "mappings": "gGAIA,MAAMA,EAAa,CCHZC,GAAG,ODIJC,EAAa,CCARC,MAAM,eDCXC,EAAa,CACjBC,IAAK,ECDMF,MAAM,kBDIbG,EAAa,CACjBD,IAAK,ECCMF,MAAM,4BDEbI,EAAa,CCMRJ,MAAM,cDLXK,EAAa,CACjBH,IAAK,ECQiCF,MAAM,aDLxCM,EAAa,CAAC,MAAO,OACrBC,EAAa,CACjBL,IAAK,ECmCiCF,MAAM,aDhCxCQ,EAAa,CCqCRR,MAAM,eDpCXS,EAAc,CAAC,MAAO,OACtBC,EAAc,CAClBR,IAAK,ECgEiCF,MAAM,aD7DxCW,EAAc,CCiEPX,MAAM,uBD/Db,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAA+BD,EAAAA,EAAAA,IAAkB,qBACjDE,GAAyBF,EAAAA,EAAAA,IAAkB,eAEjD,OAAQG,EAAAA,EAAAA,OCpCRC,EAAAA,EAAAA,IA0GM,MA1GN3B,EA0GM,EAxGJ4B,EAAAA,EAAAA,IAkGM,OAlGDzB,OAAK0B,EAAAA,EAAAA,IAAA,CAAC,UAAS,CAAAC,UAAsBd,EAAAe,oBDqCvC,ECnCDH,EAAAA,EAAAA,IAYM,MAZN1B,EAYM,CAX+Bc,EAAAe,kBDmD9BL,EAAAA,EAAAA,OC7CLC,EAAAA,EAAAA,IAIM,MAJNrB,EAIM,EAHJ0B,EAAAA,EAAAA,IAEcV,EAAA,CAFDW,GAAG,KAAG,CD8CbC,SAASC,EAAAA,EAAAA,IC7Cb,IAA6DlB,EAAA,KAAAA,EAAA,KAA7DW,EAAAA,EAAAA,IAA6D,OAAxDQ,IANAC,EAMwBC,IAAI,OAAOnC,MAAM,cDkDrC,MAAO,MAEZoC,EAAG,EACHC,GAAI,CAAC,UAxBRd,EAAAA,EAAAA,OCrCLC,EAAAA,EAAAA,IAKM,MALNvB,EAKM,EAJJ4B,EAAAA,EAAAA,IAEcV,EAAA,CAFDW,GAAG,KAAG,CDsCbC,SAASC,EAAAA,EAAAA,ICrCb,IAAuDlB,EAAA,KAAAA,EAAA,KAAvDW,EAAAA,EAAAA,IAAuD,OAAlDQ,IAAAC,EAAwBC,IAAI,OAAOnC,MAAM,QD0CrC,MAAO,MAEZoC,EAAG,EACHC,GAAI,CAAC,KAEPvB,EAAO,KAAOA,EAAO,IC7CzBW,EAAAA,EAAAA,IAAoC,QAA9BzB,MAAM,YAAW,UAAM,UAUjCyB,EAAAA,EAAAA,IAsCM,MAtCNrB,EAsCM,EApCJyB,EAAAA,EAAAA,IAGcV,EAAA,CAHDW,GAAG,IAAI9B,MAAM,YAAY,eAAa,UDsDhD,CACD+B,SAASC,EAAAA,EAAAA,ICtDT,IAAmD,EAAnDH,EAAAA,EAAAA,IAAmDR,EAAA,CAAhCiB,KAAK,OAAOtC,MAAM,cACxBa,EAAAe,iBD4DPW,EAAAA,EAAAA,IAAoB,IAAI,KADvBhB,EAAAA,EAAAA,OC3DPC,EAAAA,EAAAA,IAAyD,OAAzDnB,EAAgD,SD8DhD+B,EAAG,MAEJb,EAAAA,EAAAA,KAAW,IC5DZC,EAAAA,EAAAA,IA6BYgB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA5BI5B,EAAA6B,YAAPC,KD4DCpB,EAAAA,EAAAA,OC7DVqB,EAAAA,EAAAA,KA6BYC,EAAAA,EAAAA,IA1BLF,EAAIG,UAAYjC,EAAAkC,QAAQC,SAAW,cAAgB,OAAnB,CADpC9C,IAAKyC,EAAI7C,GAETgC,GAAIa,EAAIG,UAAYjC,EAAAkC,QAAQC,SAAWL,EAAIM,YAASC,EACrDlD,OAAK0B,EAAAA,EAAAA,IAAA,CAAC,YAAW,gBACSiB,EAAIG,UAAYjC,EAAAkC,QAAQI,YAClD,eAAa,SACZC,QAAKC,GAAExC,EAAAyC,gBAAgBX,EAAKU,ID2D1B,CACDtB,SAASC,EAAAA,EAAAA,ICzDX,IAME,CALMW,EAAIY,YAAc1C,EAAA2C,iBAAiBb,EAAIL,QD0DtCf,EAAAA,EAAAA,OC3DTC,EAAAA,EAAAA,IAME,ODsDQtB,IAAK,EC1DZ+B,IAAKpB,EAAA4C,cAAcd,EAAI7C,IACvBqC,IAAKQ,EAAIe,KACV1D,MAAM,gBACL2D,QAAK7C,EAAA,KAAAA,EAAA,GD6DlB,IAAI8C,IC7DgB/C,EAAAgD,iBAAAhD,EAAAgD,mBAAAD,KD8DC,KAAM,GAAItD,IC3DRqC,EAAIL,OAASzB,EAAA2C,iBAAiBb,EAAIL,QD6DpCf,EAAAA,EAAAA,OC9DXqB,EAAAA,EAAAA,IAIEvB,EAAA,CD2DUnB,IAAK,EC7DdoC,KAAMK,EAAIL,KACXtC,MAAM,aD+DK,KAAM,EAAG,CAAC,YACZuB,EAAAA,EAAAA,OC9DXqB,EAAAA,EAAAA,IAIEvB,EAAA,CD2DUnB,IAAK,EC7DfoC,KAAK,OACLtC,MAAM,eAEKa,EAAAe,iBDgELW,EAAAA,EAAAA,IAAoB,IAAI,KADvBhB,EAAAA,EAAAA,OC/DTC,EAAAA,EAAAA,IAAqE,OAArEjB,GAAqEuD,EAAAA,EAAAA,IAAlBnB,EAAIe,MAAI,MDkEzDtB,EAAG,GACF,KAAM,CAAC,KAAM,QAAS,cACvB,SC/DNX,EAAAA,EAAAA,IAuCM,MAvCNjB,EAuCM,GD2BHe,EAAAA,EAAAA,KAAW,IChEZC,EAAAA,EAAAA,IA6BYgB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA5BI5B,EAAAkD,eAAPpB,KDgECpB,EAAAA,EAAAA,OCjEVqB,EAAAA,EAAAA,KA6BYC,EAAAA,EAAAA,IA1BLF,EAAIG,UAAYjC,EAAAkC,QAAQC,SAAW,cAAgB,OAAnB,CADpC9C,IAAKyC,EAAI7C,GAETgC,GAAIa,EAAIG,UAAYjC,EAAAkC,QAAQC,SAAWL,EAAIM,YAASC,EACrDlD,OAAK0B,EAAAA,EAAAA,IAAA,CAAC,YAAW,gBACSiB,EAAIG,UAAYjC,EAAAkC,QAAQI,YAClD,eAAa,SACZC,QAAKC,GAAExC,EAAAyC,gBAAgBX,EAAKU,ID+D1B,CACDtB,SAASC,EAAAA,EAAAA,IC7DX,IAME,CALMW,EAAIY,YAAc1C,EAAA2C,iBAAiBb,EAAIL,QD8DtCf,EAAAA,EAAAA,OC/DTC,EAAAA,EAAAA,IAME,OD0DQtB,IAAK,EC9DZ+B,IAAKpB,EAAA4C,cAAcd,EAAI7C,IACvBqC,IAAKQ,EAAIe,KACV1D,MAAM,gBACL2D,QAAK7C,EAAA,KAAAA,EAAA,GDiElB,IAAI8C,ICjEgB/C,EAAAgD,iBAAAhD,EAAAgD,mBAAAD,KDkEC,KAAM,GAAInD,IC/DRkC,EAAIL,OAASzB,EAAA2C,iBAAiBb,EAAIL,QDiEpCf,EAAAA,EAAAA,OClEXqB,EAAAA,EAAAA,IAIEvB,EAAA,CD+DUnB,IAAK,ECjEdoC,KAAMK,EAAIL,KACXtC,MAAM,aDmEK,KAAM,EAAG,CAAC,YACZuB,EAAAA,EAAAA,OClEXqB,EAAAA,EAAAA,IAIEvB,EAAA,CD+DUnB,IAAK,ECjEfoC,KAAK,OACLtC,MAAM,eAEKa,EAAAe,iBDoELW,EAAAA,EAAAA,IAAoB,IAAI,KADvBhB,EAAAA,EAAAA,OCnETC,EAAAA,EAAAA,IAAqE,OAArEd,GAAqEoD,EAAAA,EAAAA,IAAlBnB,EAAIe,MAAI,MDsEzDtB,EAAG,GACF,KAAM,CAAC,KAAM,QAAS,cACvB,OCpEJX,EAAAA,EAAAA,IAIM,MAJNd,EAIM,EAHJc,EAAAA,EAAAA,IAEM,OAFDzB,MAAM,cAAeoD,QAAKtC,EAAA,KAAAA,EAAA,GDyEzC,IAAI8C,ICzEuC/C,EAAAmD,YAAAnD,EAAAmD,cAAAJ,KD0E9B,ECzED/B,EAAAA,EAAAA,IAAgFR,EAAA,CAA5DiB,KAAMzB,EAAAe,gBAAkB,gBAAkB,gBD4E3D,KAAM,EAAG,CAAC,gBAIlB,ICzEHH,EAAAA,EAAAA,IAEM,OAFDzB,OAAK0B,EAAAA,EAAAA,IAAA,CAAC,eAAc,CAAAuC,SAAqBpD,EAAAe,oBD4E3C,EC3EDC,EAAAA,EAAAA,IAAeP,ID6Ed,IAEP,C,0DCpEA,GAAe4C,EAAAA,EAAAA,IAAgB,CAC7BR,KAAM,MACNS,WAAY,CACVC,gBAAeA,EAAAA,IAEjBC,KAAAA,GACE,MAAMzC,GAAkB0C,EAAAA,EAAAA,KAAI,GACtBC,GAAWD,EAAAA,EAAAA,IAAgB,IAE3BN,EAAaA,KACjBpC,EAAgB4C,OAAS5C,EAAgB4C,OAIrC9B,GAAc+B,EAAAA,EAAAA,IAAS,IACpBF,EAASC,MACbE,OAAO/B,GAAOA,EAAIgC,eAClBC,KAAK,CAACC,EAAGC,IAAMD,EAAEE,aAAeD,EAAEC,eAIjChB,GAAiBU,EAAAA,EAAAA,IAAS,IACvBF,EAASC,MACbE,OAAO/B,GAAOA,EAAIqC,kBAClBJ,KAAK,CAACC,EAAGC,IAAMD,EAAEE,aAAeD,EAAEC,eAIjCE,EAAeC,UACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,UAAUC,UACxCf,EAASC,MAAQW,EAASI,I,CAC1B,MAAOC,GACPC,QAAQD,MAAM,cAAeA,E,GAK3BlC,EAAkB4B,MAAOvC,EAAe+C,KACxC/C,EAAIG,UAAYC,EAAAA,GAAQI,WAE1BuC,EAAMC,uBACAC,EAAUjD,KAMdiD,EAAYV,UAChB,GAAKvC,EAAIkD,UAAT,OAMMC,EAAAA,EAAaC,QAAQ,SAASpD,EAAIe,UAAW,KAAM,CACvDsC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,SAGR,UACQd,EAAAA,GAAOC,UAAUc,OAAO,CAAEC,MAAOzD,EAAI7C,KAC3C2F,QAAQY,IAAI,SAAS1D,EAAIe,Q,CACzB,MAAO8B,GACPC,QAAQD,MAAM,YAAaA,GAC3BC,QAAQD,MAAMA,EAAML,UAAUI,MAAMe,SAAW,OAAO3D,EAAIe,W,OAf1D+B,QAAQD,MAAM,OAAO7C,EAAIe,oBAoBvBF,EAAoB+C,IACxB,IAAKA,EAAU,OAAO,EACtB,MAAMC,EAAWD,EAASE,YAAY,KACtC,OAAOD,EAAW,GAAKA,EAAWD,EAASG,OAAS,GAIhDjD,EAAiB2C,GACdhB,EAAAA,GAAOC,UAAUsB,WAAWP,GAI/BvC,EAAmB6B,IACvB,MAAMkB,EAASlB,EAAMkB,OACrBA,EAAOC,MAAMC,QAAU,QAOzB,OAJAC,EAAAA,EAAAA,IAAU,KACR9B,MAGK,CACLrD,kBACAoC,aACAtB,cACAqB,iBACAT,kBACAE,mBACAC,gBACAI,kBACAd,QAAOA,EAAAA,GAEX,I,UCpNF,MAAMiE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASpG,KAEpE,Q,UCPA,MAAMf,EAAa,CCDZG,MAAM,QDEPD,EAAa,CACjBG,IAAK,ECKiBF,MAAM,qBDFxBC,EAAa,CACjBC,IAAK,ECMSF,MAAM,cDHhBG,EAAa,CACjBD,IAAK,ECQgEF,MAAM,sBDLvEI,EAAa,CCUNJ,MAAM,aDTbK,EAAa,CAAC,MAAO,OACrBC,EAAa,CACjBJ,IAAK,ECoB6CF,MAAM,iBDhBpD,SAAUY,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAM+F,GAAyB7F,EAAAA,EAAAA,IAAkB,eAC3C8F,GAAoB9F,EAAAA,EAAAA,IAAkB,UACtCC,GAA+BD,EAAAA,EAAAA,IAAkB,qBACjD+F,GAAqB/F,EAAAA,EAAAA,IAAkB,WAE7C,OAAQG,EAAAA,EAAAA,OC3BRC,EAAAA,EAAAA,IA4CM,MA5CN3B,EA4CM,CDhBJiB,EAAO,KAAOA,EAAO,IC1BrBW,EAAAA,EAAAA,IAGM,OAHDzB,MAAM,eAAa,EACtByB,EAAAA,EAAAA,IAAe,UAAX,WACJA,EAAAA,EAAAA,IAAwB,SAArB,uBD2BD,ICvBOZ,EAAAuG,UDyBN7F,EAAAA,EAAAA,OCzBLC,EAAAA,EAAAA,IAEM,MAFNzB,EAEM,EADJ8B,EAAAA,EAAAA,IAAkCoF,EAAA,CAApBI,KAAM,EAAGC,SAAA,UD8BpB/F,EAAAA,EAAAA,OC1BLC,EAAAA,EAAAA,IA8BM,MA9BNvB,EA8BM,GDHCsB,EAAAA,EAAAA,KAAW,ICzBhBC,EAAAA,EAAAA,IA2BUgB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA3Ba5B,EAAA0G,gBAAP5E,KD0BFpB,EAAAA,EAAAA,OC1BdqB,EAAAA,EAAAA,IA2BUuE,EAAA,CA3B+BjH,IAAKyC,EAAI7C,GAAIE,OAAK0B,EAAAA,EAAAA,IAAA,CAAC,8BAA6B,CD6BvF,mBC7B8HiB,EAAIkD,aAEjI2B,OAAO,QAASpE,QAAKC,GAAExC,EAAA4G,eAAe9E,ID+BhC,CACDZ,SAASC,EAAAA,EAAAA,IC9Bf,IAEM,CAFMW,EAAIkD,WAAalD,EAAIG,UAAYjC,EAAAkC,QAAQI,UD4CzCZ,EAAAA,EAAAA,IAAoB,IAAI,KAZvBhB,EAAAA,EAAAA,OChCbC,EAAAA,EAAAA,IAEM,MAFNrB,EAEM,EADJ0B,EAAAA,EAAAA,IAA+CqF,EAAA,CAAvCQ,KAAK,QAAQxB,KAAK,UDmCX,CACDnE,SAASC,EAAAA,EAAAA,ICpCY,IAAGlB,EAAA,KAAAA,EAAA,KDqCtB6G,EAAAA,EAAAA,ICrCmB,UDuCrBvF,EAAG,EACHC,GAAI,CAAC,SCpCrBZ,EAAAA,EAAAA,IAMM,MANNrB,EAMM,CALOuC,EAAIY,YAAc1C,EAAA2C,iBAAiBb,EAAIL,QDyCrCf,EAAAA,EAAAA,OCzCbC,EAAAA,EAAAA,IACsD,ODyCxCtB,IAAK,EC1CuC+B,IAAKpB,EAAA4C,cAAcd,EAAI7C,IAAMqC,IAAKQ,EAAIe,KAC9F1D,MAAM,WAAY2D,QAAK7C,EAAA,KAAAA,EAAA,GAAAuC,GAAExC,EAAAgD,gBAAgBR,KD8C5B,KAAM,GAAIhD,IC7CKsC,EAAIL,OAASzB,EAAA2C,iBAAiBb,EAAIL,QD+CjDf,EAAAA,EAAAA,OC/CfqB,EAAAA,EAAAA,IAC6BvB,EAAA,CD+CbnB,IAAK,EChDmDoC,KAAMK,EAAIL,KAChFtC,MAAM,oBDkDS,KAAM,EAAG,CAAC,YACZuB,EAAAA,EAAAA,OClDfqB,EAAAA,EAAAA,IAAiEvB,EAAA,CDmDjDnB,IAAK,ECnDKoC,KAAK,OAAOtC,MAAM,yBAI9CyB,EAAAA,EAAAA,IAAwC,WAAAqC,EAAAA,EAAAA,IAAjCnB,EAAIe,MAAQ,eAAJ,IACfjC,EAAAA,EAAAA,IAAuC,UAAAqC,EAAAA,EAAAA,IAAjCnB,EAAIiF,aAAe,SAAJ,GAGVjF,EAAIkF,MAAQlF,EAAIkF,KAAKnB,OAAS,IDmD5BnF,EAAAA,EAAAA,OCnDbC,EAAAA,EAAAA,IAIM,MAJNlB,EAIM,GDgDSiB,EAAAA,EAAAA,KAAW,ICnDxBC,EAAAA,EAAAA,IAESgB,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAFaE,EAAIkF,KAAXC,KDoDOvG,EAAAA,EAAAA,OCpDtBqB,EAAAA,EAAAA,IAESsE,EAAA,CAFwBhH,IAAK4H,EAAKJ,KAAK,QAASxB,KAAMrF,EAAAkH,YAAYD,IDwD1D,CACD/F,SAASC,EAAAA,EAAAA,ICxDvB,IAAS,EDyDO2F,EAAAA,EAAAA,KAAiB7D,EAAAA,EAAAA,ICzD9BgE,GAAG,KD2DQ1F,EAAG,GACF,KAAM,CAAC,WACR,UAENG,EAAAA,EAAAA,IAAoB,IAAI,KAE9BH,EAAG,GACF,KAAM,CAAC,QAAS,cACjB,UAGd,C,sBCvDA,GAAe8B,EAAAA,EAAAA,IAAgB,CAC7BR,KAAM,WACNS,WAAY,CACVC,gBAAeA,EAAAA,IAEjBC,KAAAA,GACE,MAAM2D,GAASC,EAAAA,EAAAA,MACTC,GAAa5D,EAAAA,EAAAA,IAAgB,IAC7B8C,GAAU9C,EAAAA,EAAAA,KAAI,GAEd6D,EAAkBC,IACtBJ,EAAOK,KAAKD,IAIRb,GAAkB9C,EAAAA,EAAAA,IAAS,IACxByD,EAAW1D,MACfE,OAAO/B,GAAOA,EAAI2F,gBAClB1D,KAAK,CAACC,EAAGC,IAAMD,EAAEE,aAAeD,EAAEC,eAIjCgD,EAAeD,IACnB,MAAMS,EAAS,CAAC,GAAI,UAAW,UAAW,QAC1C,IAAIC,EAAO,EACX,IAAK,IAAIC,EAAI,EAAGA,EAAIX,EAAIpB,OAAQ+B,IAC9BD,EAAOV,EAAIY,WAAWD,KAAOD,GAAQ,GAAKA,GAE5C,OAAOD,EAAOI,KAAKC,IAAIJ,GAAQD,EAAO7B,SAIlCjD,EAAiB2C,GACdhB,EAAAA,GAAOC,UAAUsB,WAAWP,GAI/BvC,EAAmB6B,IACvB,MAAMkB,EAASlB,EAAMkB,OACrBA,EAAOC,MAAMC,QAAU,QAKnBtD,EAAoB+C,IACxB,IAAKA,EAAU,OAAO,EACtB,MAAMC,EAAWD,EAASE,YAAY,KACtC,OAAOD,EAAW,GAAKA,EAAWD,EAASG,OAAS,GAIhDmC,EAAiB3D,UACrB,IACEkC,EAAQ5C,OAAQ,EAChB,MAAMW,QAAiBC,EAAAA,GAAOC,UAAUC,UACxC4C,EAAW1D,MAAQW,EAASI,I,CAC5B,MAAOC,GACPC,QAAQD,MAAM,YAAaA,E,CAE7B,QACE4B,EAAQ5C,OAAQ,C,GAKdiD,EAAiBvC,UACjBvC,EAAIG,UAAYC,EAAAA,GAAQC,SAEtBL,EAAIM,QACNkF,EAAexF,EAAIM,cAIf2C,EAAUjD,IAKdiD,EAAYV,UAChB,GAAKvC,EAAIkD,UAAT,OAKMC,EAAAA,EAAaC,QAAQ,SAASpD,EAAIe,UAAW,KAAM,CACvDsC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,SAGR,UACQd,EAAAA,GAAOC,UAAUc,OAAO,CAAEC,MAAOzD,EAAI7C,KAC3CgJ,EAAAA,GAAUC,QAAQ,SAASpG,EAAIe,W,CAC/B,MAAO8B,GACPC,QAAQD,MAAM,UAAWA,GACzBsD,EAAAA,GAAUtD,MAAMA,EAAML,UAAUI,MAAMe,SAAW,OAAO3D,EAAIe,W,OAf5DoF,EAAAA,GAAUtD,MAAM,OAAO7C,EAAIe,oBAuB/B,OAJAqD,EAAAA,EAAAA,IAAU,KACR8B,MAGK,CACLV,iBACAD,aACAX,kBACAH,UACAW,cACAN,iBACA7B,YACAnC,gBACAI,kBACAL,mBACAT,QAAOA,EAAAA,GAEX,ICpKF,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QCNA,MAAMiG,EAAgC,CACpC,CACEZ,KAAM,IACN1E,KAAM,OACNuF,UAAWC,GAEb,CACEd,KAAM,iBACN1E,KAAM,gBACNuF,UAAWA,IACT,+BAEJ,CACEb,KAAM,kBACN1E,KAAM,iBACNuF,UAAWA,IACT,+BAEJ,CACEb,KAAM,SACN1E,KAAM,QACNuF,UAAWA,IACT,gCAIAjB,GAASmB,EAAAA,EAAAA,IAAa,CAC1BC,SAASC,EAAAA,EAAAA,IAAiBC,KAC1BN,WAGF,Q,SChCA,GAAeO,EAAAA,EAAAA,IAAY,CACzBC,MAAO,CAAC,EACRC,QAAS,CAAC,EACVC,UAAW,CAAC,EACZC,QAAS,CAAC,EACVC,QAAS,CAAC,I,UCHZ,MAAMC,EAAsBrE,IAC1B,IAAKA,EAAML,WAAaK,EAAML,SAASI,KACrC,OAAOC,EAAMc,SAAW,gBAG1B,MAAMwD,EAAYtE,EAAML,SAASI,KAC3BwE,EAAgB,GAGlBD,EAAUE,kBACZD,EAAc1B,KAAKyB,EAAUE,kBAI/B,IAAIC,EAAmBH,EAAUI,eACjC,MAAOD,EACDA,EAAiBD,kBACnBD,EAAc1B,KAAK4B,EAAiBD,kBAEtCC,EAAmBA,EAAiBC,eAItC,OAA6B,IAAzBH,EAAcrD,OACToD,EAAUxD,SAAW,oBAIvByD,EAAcI,KAAK,SAItBC,EAAqB5E,IACzB,IAAKA,EAAML,WAAaK,EAAML,SAASI,KAErC,YADAuD,EAAAA,GAAUtD,MAAMA,EAAMc,SAAW,iBAKnC,MAAM+D,EAAeR,EAAmBrE,GAGxCM,EAAAA,EAAawE,MACXD,EACA,QACA,CACErE,kBAAmB,KACnBuE,0BAA0B,EAC1BC,mBAAmB,EACnBC,oBAAoB,EACpBC,WAAW,KAMXC,EAAkBnF,IAEtB,GAAIA,EAAML,UAAYK,EAAML,SAASI,KAAM,CAEzC,GAA4B,iBAAxBC,EAAML,SAASI,KACjB,OAAO,EAIT,GAAoC,iBAAhCC,EAAML,SAASI,KAAKe,QACtB,OAAO,EAIT,GAAsC,iBAAlCd,EAAML,SAASI,KAAKqF,UACtB,OAAO,C,CAIX,OAAO,GAIIC,EAAoBA,KAC/BC,EAAAA,EAAMC,aAAa5F,SAAS6F,IAC1B7F,GAAYA,EACZK,GAEMmF,EAAenF,IAEjBsD,EAAAA,GAAUmC,KAAK,+BAGRC,QAAQC,OAAO3F,KAIxB4E,EAAkB5E,GAGX0F,QAAQC,OAAO3F,MAK5B,I,yECjEA4F,GAAAA,GAAQC,IACNC,GAAAA,IAAQC,GAAAA,IAAcC,GAAAA,IAAQC,GAAAA,IAAWC,GAAAA,IACzCC,GAAAA,IAAWC,GAAAA,IAAYC,GAAAA,IAAaC,GAAAA,GACpCC,GAAAA,IAAUC,GAAAA,IAAYC,GAAAA,IACtBC,GAAAA,IAAmBC,GAAAA,IACnBC,GAAAA,IAAaC,GAAAA,IAAWC,GAAAA,IAAUC,GAAAA,IAClCC,GAAAA,IAAgCC,GAAAA,IAChCC,GAAAA,IAAQC,GAAAA,IAAeC,GAAAA,IAAcC,GAAAA,IAAeC,GAAAA,IACpDC,GAAAA,IAAOC,GAAAA,IAAQC,GAAAA,IAAQC,GAAAA,IAAWC,GAAAA,IAAUC,GAAAA,IAAYC,GAAAA,IACxDC,GAAAA,IAAQC,GAAAA,IAAYC,GAAAA,IAASC,GAAAA,IAAUC,GAAAA,IAAaC,GAAAA,IAAcC,GAAAA,IAClEC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAC9BC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAC9BC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IACzBC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IACzBC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,IAAKC,GAAAA,KAG/C,MAAMrN,IAAMsN,EAAAA,EAAAA,IAAUC,GAGtBvN,GAAIsG,UAAU,oBAAqB7E,EAAAA,IAGnC,IAAK,MAAOlE,GAAK+I,MAAckH,OAAOC,QAAQC,GAC5C1N,GAAIsG,UAAU/I,GAAK+I,IAIrB4B,IAEAlI,GAAIqI,IAAIsF,GACJtF,IAAIhD,GACJgD,IAAIuF,EAAAA,EAAa,CAChBC,OAAQC,EAAAA,EACR/I,KAAM,YAEPgJ,MAAM,QAMV/N,GAAIgO,OAAOC,aAAe,CAACC,EAAcC,EAAI7F,KAE3CxF,QAAQD,MAAM,YAAaqL,GAG3B,MAAM/G,EAAuB,CAC3BxD,QAASuK,aAAeE,MAAQF,EAAIvK,QAAU0K,OAAOH,GACrDI,MAAOJ,aAAeE,MAAQF,EAAII,MAAQ,QAC1CC,YAAajG,EACbkG,IAAKC,OAAOC,SAASC,MAGvBlM,EAAAA,GAAOmM,SAASzH,GAAW0H,MAAOC,IAChChM,QAAQD,MAAM,cAAeiM,MAKjCL,OAAOM,iBAAiB,qBAAuBhM,IAC7C,MAAMoE,EAAuB,CAC3BxD,QACEZ,EAAMiM,kBAAkBZ,MACpBrL,EAAMiM,OAAOrL,QACb,gBACN2K,MAAOvL,EAAMiM,kBAAkBZ,MAAQrL,EAAMiM,OAAOV,MAAQ,QAC5DE,IAAKC,OAAOC,SAASC,KACrBpL,KAAM,sBAGRd,EAAAA,GAAOmM,SAASzH,GAAW0H,MAAOC,IAChChM,QAAQD,MAAM,qBAAsBiM,OAKxCL,OAAOM,iBAAiB,QAAUhM,IAEhC,GAAIA,EAAMY,QAAS,CACjB,MAAMwD,EAAuB,CAC3BxD,QAASZ,EAAMY,QACfsL,SAAU,GAAGlM,EAAMa,YAAYb,EAAMmM,UAAUnM,EAAMoM,QACrDX,IAAKC,OAAOC,SAASC,KACrBpL,KAAM,gBAGRd,EAAAA,GAAOmM,SAASzH,GAAW0H,MAAOC,IAChChM,QAAQD,MAAM,gBAAiBiM,I,uLCxGzBM,EAgCAC,EAUAjP,EAuEAkP,EAMAC,EAOAC,E,WA9HZ,SAAYJ,GACVA,EAAAA,EAAA,wBACAA,EAAAA,EAAA,gBACAA,EAAAA,EAAA,eACD,EAJD,CAAYA,IAAAA,EAAa,KAgCzB,SAAYC,GACVA,EAAA,qBACAA,EAAA,2BACAA,EAAA,yBACAA,EAAA,mBACAA,EAAA,wBACD,CAND,CAAYA,IAAAA,EAAa,KAUzB,SAAYjP,GACVA,EAAA,uBACAA,EAAA,sBACD,CAHD,CAAYA,IAAAA,EAAO,KAuEnB,SAAYkP,GACVA,EAAA,iBACAA,EAAA,eACAA,EAAA,YACD,CAJD,CAAYA,IAAAA,EAAc,KAM1B,SAAYC,GACVA,EAAA,qBACAA,EAAA,qBACAA,EAAA,mBACAA,EAAA,gBACD,CALD,CAAYA,IAAAA,EAAgB,KAO5B,SAAYC,GACVA,EAAA,mBACAA,EAAA,qBACAA,EAAA,mBACAA,EAAA,qBACAA,EAAA,eACAA,EAAA,iBACAA,EAAA,kBACD,CARD,CAAYA,IAAAA,EAAS,KAqDrB,MAAMC,EAAW,WACXC,EAAmB,sBACnBC,EAAoB,gBACpBC,EAAsB,iBACtBC,EAAyB,oBAElBpN,EAAS,CAEpBqN,UAAAA,GACE,OAAO3H,EAAAA,EAAM4H,IAAI,GAAGN,YACtB,EAGAb,SAAWzH,GACFgB,EAAAA,EAAM6H,KAAK,GAAGP,aAAqBtI,GAI5C8I,KAAMA,IACG9H,EAAAA,EAAM6H,KAAK,GAAGP,UAIvBS,YAAAA,GACE,OAAO/H,EAAAA,EAAM4H,IAAI,iBACnB,EAGAI,eAAgB,CAEdC,UAAAA,GACE,OAAOjI,EAAAA,EAAM6H,KAAK,GAAGN,gBACvB,EAGAW,YAAAA,CAAaC,GACX,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGN,UAA0BY,EACjD,EAGAC,WAAAA,CAAYC,GACV,OAAOrI,EAAAA,EAAM4H,IAAI,GAAGL,qBAAoCc,IAC1D,EAGAC,aAAAA,CAAcD,GACZ,OAAOrI,EAAAA,EAAM6H,KAAK,GAAGN,WAA2B,KAAM,CAAEgB,OAAQ,CAAEF,WACpE,GAIFG,SAAU,CAERC,YAAAA,GACE,OAAOzI,EAAAA,EAAM4H,IAAI,GAAGJ,kBACtB,EAGAkB,YAAAA,CAAapL,GACX,OAAO0C,EAAAA,EAAM4H,IAAI,GAAGJ,kBAAmC,CAAEe,OAAQ,CAAEjL,SACrE,EAGA4K,YAAAA,CAAa5K,GACX,OAAO0C,EAAAA,EAAM4H,IAAI,GAAGJ,kBAAmC,CAAEe,OAAQ,CAAEjL,SACrE,GAIF/C,UAAW,CAETC,OAAAA,GACE,OAAOwF,EAAAA,EAAM4H,IAAI,GAAGH,SACtB,EAGApM,MAAAA,CAAO8M,GACL,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGJ,WAA8BU,EACrD,EAGAtM,UAAAA,CAAWP,GACT,MAAO,GAAGmM,gBAAkCnM,GAC9C,GAIFqN,aAAc,CAEZC,YAAAA,GACE,OAAO5I,EAAAA,EAAM4H,IAAI,GAAGF,cACtB,EAGAO,UAAAA,GACE,OAAOjI,EAAAA,EAAM6H,KAAK,GAAGH,gBACvB,EAGAmB,SAAAA,CAAUV,GACR,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGH,UAAgCS,EACvD,EAGAW,WAAAA,CAAYX,GACV,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGH,YAAkCS,EACzD,EAKAY,iBAAAA,GACE,OAAO/I,EAAAA,EAAM6H,KAAK,GAAGH,wBACvB,EAGAsB,cAAAA,CAAeb,GACb,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGH,qBAA2CS,EAClE,EAGAc,cAAAA,GACE,OAAOjJ,EAAAA,EAAM4H,IAAI,GAAGF,iBACtB,EAGAwB,eAAAA,CAAgBf,GACd,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGH,sBAA4CS,EACnE,EAGAgB,gBAAAA,CAAiBhB,GACf,OAAOnI,EAAAA,EAAM6H,KAAK,GAAGH,uBAA6CS,EACpE,EAGAiB,gBAAAA,GACE,OAAOpJ,EAAAA,EAAM6H,KAAK,GAAGH,uBACvB,EAGA2B,cAAAA,GACE,OAAOrJ,EAAAA,EAAM4H,IAAI,GAAGF,iBACtB,EAGA4B,gBAAAA,GACE,OAAOtJ,EAAAA,EAAM6H,KAAK,GAAGH,uBACvB,G,+DC9VA6B,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBrR,IAAjBsR,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CAGjDE,QAAS,CAAC,GAOX,OAHAE,EAAoBJ,GAAUK,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAGpEI,EAAOD,OACf,CAGAH,EAAoBO,EAAIF,E,WCzBxB,IAAIG,EAAW,GACfR,EAAoBS,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAAS5M,EAAI,EAAGA,EAAIqM,EAASpO,OAAQ+B,IAAK,CACrCwM,EAAWH,EAASrM,GAAG,GACvByM,EAAKJ,EAASrM,GAAG,GACjB0M,EAAWL,EAASrM,GAAG,GAE3B,IAJA,IAGI6M,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASvO,OAAQ6O,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAahF,OAAOqF,KAAKlB,EAAoBS,GAAGU,MAAM,SAASvV,GAAO,OAAOoU,EAAoBS,EAAE7U,GAAK+U,EAASM,GAAK,GAChKN,EAASS,OAAOH,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbR,EAASY,OAAOjN,IAAK,GACrB,IAAIkN,EAAIT,SACEhS,IAANyS,IAAiBX,EAASW,EAC/B,CACD,CACA,OAAOX,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAI1M,EAAIqM,EAASpO,OAAQ+B,EAAI,GAAKqM,EAASrM,EAAI,GAAG,GAAK0M,EAAU1M,IAAKqM,EAASrM,GAAKqM,EAASrM,EAAI,GACrGqM,EAASrM,GAAK,CAACwM,EAAUC,EAAIC,EAwB/B,C,eC5BAb,EAAoBsB,EAAI,SAASlB,GAChC,IAAImB,EAASnB,GAAUA,EAAOoB,WAC7B,WAAa,OAAOpB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAJ,EAAoByB,EAAEF,EAAQ,CAAEhR,EAAGgR,IAC5BA,CACR,C,eCNAvB,EAAoByB,EAAI,SAAStB,EAASuB,GACzC,IAAI,IAAI9V,KAAO8V,EACX1B,EAAoB2B,EAAED,EAAY9V,KAASoU,EAAoB2B,EAAExB,EAASvU,IAC5EiQ,OAAO+F,eAAezB,EAASvU,EAAK,CAAEiW,YAAY,EAAMzD,IAAKsD,EAAW9V,IAG3E,C,eCPAoU,EAAoB8B,EAAI,CAAC,EAGzB9B,EAAoB+B,EAAI,SAASC,GAChC,OAAOpL,QAAQqL,IAAIpG,OAAOqF,KAAKlB,EAAoB8B,GAAGI,OAAO,SAASC,EAAUvW,GAE/E,OADAoU,EAAoB8B,EAAElW,GAAKoW,EAASG,GAC7BA,CACR,EAAG,IACJ,C,eCPAnC,EAAoBoC,EAAI,SAASJ,GAEhC,MAAO,MAAQ,CAAC,IAAM,gBAAgB,IAAM,QAAQ,IAAM,kBAAkBA,GAAW,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,KAC9J,C,eCHAhC,EAAoBqC,SAAW,SAASL,GAEvC,MAAO,OAAS,CAAC,IAAM,gBAAgB,IAAM,QAAQ,IAAM,kBAAkBA,GAAW,IAAM,CAAC,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,MAC/J,C,eCJAhC,EAAoBsC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOV,GACR,GAAsB,kBAAXjF,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBkD,EAAoB2B,EAAI,SAASe,EAAKC,GAAQ,OAAO9G,OAAO+G,UAAUC,eAAevC,KAAKoC,EAAKC,EAAO,C,eCAtG,IAAIG,EAAa,CAAC,EACdC,EAAoB,eAExB/C,EAAoBgD,EAAI,SAASnG,EAAKoG,EAAMrX,EAAKoW,GAChD,GAAGc,EAAWjG,GAAQiG,EAAWjG,GAAK9I,KAAKkP,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAWvU,IAARhD,EAEF,IADA,IAAIwX,EAAUC,SAASC,qBAAqB,UACpCnP,EAAI,EAAGA,EAAIiP,EAAQhR,OAAQ+B,IAAK,CACvC,IAAIoP,EAAIH,EAAQjP,GAChB,GAAGoP,EAAEC,aAAa,QAAU3G,GAAO0G,EAAEC,aAAa,iBAAmBT,EAAoBnX,EAAK,CAAEsX,EAASK,EAAG,KAAO,CACpH,CAEGL,IACHC,GAAa,EACbD,EAASG,SAASI,cAAc,UAEhCP,EAAOQ,QAAU,QACjBR,EAAOS,QAAU,IACb3D,EAAoB4D,IACvBV,EAAOW,aAAa,QAAS7D,EAAoB4D,IAElDV,EAAOW,aAAa,eAAgBd,EAAoBnX,GAExDsX,EAAOvV,IAAMkP,GAEdiG,EAAWjG,GAAO,CAACoG,GACnB,IAAIa,EAAmB,SAASC,EAAM3S,GAErC8R,EAAOc,QAAUd,EAAOe,OAAS,KACjCC,aAAaP,GACb,IAAIQ,EAAUrB,EAAWjG,GAIzB,UAHOiG,EAAWjG,GAClBqG,EAAOkB,YAAclB,EAAOkB,WAAWC,YAAYnB,GACnDiB,GAAWA,EAAQG,QAAQ,SAAS1D,GAAM,OAAOA,EAAGxP,EAAQ,GACzD2S,EAAM,OAAOA,EAAK3S,EACtB,EACIuS,EAAUY,WAAWT,EAAiBU,KAAK,UAAM5V,EAAW,CAAEgD,KAAM,UAAWU,OAAQ4Q,IAAW,MACtGA,EAAOc,QAAUF,EAAiBU,KAAK,KAAMtB,EAAOc,SACpDd,EAAOe,OAASH,EAAiBU,KAAK,KAAMtB,EAAOe,QACnDd,GAAcE,SAASoB,KAAKC,YAAYxB,EApCkB,CAqC3D,C,eCxCAlD,EAAoBqB,EAAI,SAASlB,GACX,qBAAXwE,QAA0BA,OAAOC,aAC1C/I,OAAO+F,eAAezB,EAASwE,OAAOC,YAAa,CAAE1U,MAAO,WAE7D2L,OAAO+F,eAAezB,EAAS,aAAc,CAAEjQ,OAAO,GACvD,C,eCNA8P,EAAoB6E,EAAI,G,eCAxB,GAAwB,qBAAbxB,SAAX,CACA,IAAIyB,EAAmB,SAAS9C,EAAS+C,EAAUC,EAAQC,EAASpO,GACnE,IAAIqO,EAAU7B,SAASI,cAAc,QAErCyB,EAAQC,IAAM,aACdD,EAAQtT,KAAO,WACXoO,EAAoB4D,KACvBsB,EAAQE,MAAQpF,EAAoB4D,IAErC,IAAIyB,EAAiB,SAASjU,GAG7B,GADA8T,EAAQlB,QAAUkB,EAAQjB,OAAS,KAChB,SAAf7S,EAAMQ,KACTqT,QACM,CACN,IAAIK,EAAYlU,GAASA,EAAMQ,KAC3B2T,EAAWnU,GAASA,EAAMkB,QAAUlB,EAAMkB,OAAO0K,MAAQ+H,EACzDxI,EAAM,IAAIE,MAAM,qBAAuBuF,EAAU,cAAgBsD,EAAY,KAAOC,EAAW,KACnGhJ,EAAInN,KAAO,iBACXmN,EAAIiJ,KAAO,wBACXjJ,EAAI3K,KAAO0T,EACX/I,EAAIoC,QAAU4G,EACVL,EAAQd,YAAYc,EAAQd,WAAWC,YAAYa,GACvDrO,EAAO0F,EACR,CACD,EAUA,OATA2I,EAAQlB,QAAUkB,EAAQjB,OAASoB,EACnCH,EAAQlI,KAAO+H,EAGXC,EACHA,EAAOZ,WAAWqB,aAAaP,EAASF,EAAOU,aAE/CrC,SAASoB,KAAKC,YAAYQ,GAEpBA,CACR,EACIS,EAAiB,SAAS3I,EAAM+H,GAEnC,IADA,IAAIa,EAAmBvC,SAASC,qBAAqB,QAC7CnP,EAAI,EAAGA,EAAIyR,EAAiBxT,OAAQ+B,IAAK,CAChD,IAAIX,EAAMoS,EAAiBzR,GACvB0R,EAAWrS,EAAIgQ,aAAa,cAAgBhQ,EAAIgQ,aAAa,QACjE,GAAe,eAAZhQ,EAAI2R,MAAyBU,IAAa7I,GAAQ6I,IAAad,GAAW,OAAOvR,CACrF,CACA,IAAIsS,EAAoBzC,SAASC,qBAAqB,SACtD,IAAQnP,EAAI,EAAGA,EAAI2R,EAAkB1T,OAAQ+B,IAAK,CAC7CX,EAAMsS,EAAkB3R,GACxB0R,EAAWrS,EAAIgQ,aAAa,aAChC,GAAGqC,IAAa7I,GAAQ6I,IAAad,EAAU,OAAOvR,CACvD,CACD,EACIuS,EAAiB,SAAS/D,GAC7B,OAAO,IAAIpL,QAAQ,SAASqO,EAASpO,GACpC,IAAImG,EAAOgD,EAAoBqC,SAASL,GACpC+C,EAAW/E,EAAoB6E,EAAI7H,EACvC,GAAG2I,EAAe3I,EAAM+H,GAAW,OAAOE,IAC1CH,EAAiB9C,EAAS+C,EAAU,KAAME,EAASpO,EACpD,EACD,EAEImP,EAAqB,CACxB,IAAK,GAGNhG,EAAoB8B,EAAEmE,QAAU,SAASjE,EAASG,GACjD,IAAI+D,EAAY,CAAC,IAAM,EAAE,IAAM,EAAE,IAAM,GACpCF,EAAmBhE,GAAUG,EAASpO,KAAKiS,EAAmBhE,IACzB,IAAhCgE,EAAmBhE,IAAkBkE,EAAUlE,IACtDG,EAASpO,KAAKiS,EAAmBhE,GAAW+D,EAAe/D,GAASmE,KAAK,WACxEH,EAAmBhE,GAAW,CAC/B,EAAG,SAASD,GAEX,aADOiE,EAAmBhE,GACpBD,CACP,GAEF,CA3E2C,C,eCK3C,IAAIqE,EAAkB,CACrB,IAAK,GAGNpG,EAAoB8B,EAAEb,EAAI,SAASe,EAASG,GAE1C,IAAIkE,EAAqBrG,EAAoB2B,EAAEyE,EAAiBpE,GAAWoE,EAAgBpE,QAAWpT,EACtG,GAA0B,IAAvByX,EAGF,GAAGA,EACFlE,EAASpO,KAAKsS,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI1P,QAAQ,SAASqO,EAASpO,GAAUwP,EAAqBD,EAAgBpE,GAAW,CAACiD,EAASpO,EAAS,GACzHsL,EAASpO,KAAKsS,EAAmB,GAAKC,GAGtC,IAAIzJ,EAAMmD,EAAoB6E,EAAI7E,EAAoBoC,EAAEJ,GAEpD9Q,EAAQ,IAAIuL,MACZ8J,EAAe,SAASnV,GAC3B,GAAG4O,EAAoB2B,EAAEyE,EAAiBpE,KACzCqE,EAAqBD,EAAgBpE,GACX,IAAvBqE,IAA0BD,EAAgBpE,QAAWpT,GACrDyX,GAAoB,CACtB,IAAIf,EAAYlU,IAAyB,SAAfA,EAAMQ,KAAkB,UAAYR,EAAMQ,MAChE4U,EAAUpV,GAASA,EAAMkB,QAAUlB,EAAMkB,OAAO3E,IACpDuD,EAAMc,QAAU,iBAAmBgQ,EAAU,cAAgBsD,EAAY,KAAOkB,EAAU,IAC1FtV,EAAM9B,KAAO,iBACb8B,EAAMU,KAAO0T,EACbpU,EAAMyN,QAAU6H,EAChBH,EAAmB,GAAGnV,EACvB,CAEF,EACA8O,EAAoBgD,EAAEnG,EAAK0J,EAAc,SAAWvE,EAASA,EAE/D,CAEH,EAUAhC,EAAoBS,EAAEQ,EAAI,SAASe,GAAW,OAAoC,IAA7BoE,EAAgBpE,EAAgB,EAGrF,IAAIyE,EAAuB,SAASC,EAA4BzV,GAC/D,IAKIgP,EAAU+B,EALVrB,EAAW1P,EAAK,GAChB0V,EAAc1V,EAAK,GACnB2V,EAAU3V,EAAK,GAGIkD,EAAI,EAC3B,GAAGwM,EAASkG,KAAK,SAASrb,GAAM,OAA+B,IAAxB4a,EAAgB5a,EAAW,GAAI,CACrE,IAAIyU,KAAY0G,EACZ3G,EAAoB2B,EAAEgF,EAAa1G,KACrCD,EAAoBO,EAAEN,GAAY0G,EAAY1G,IAGhD,GAAG2G,EAAS,IAAIlG,EAASkG,EAAQ5G,EAClC,CAEA,IADG0G,GAA4BA,EAA2BzV,GACrDkD,EAAIwM,EAASvO,OAAQ+B,IACzB6N,EAAUrB,EAASxM,GAChB6L,EAAoB2B,EAAEyE,EAAiBpE,IAAYoE,EAAgBpE,IACrEoE,EAAgBpE,GAAS,KAE1BoE,EAAgBpE,GAAW,EAE5B,OAAOhC,EAAoBS,EAAEC,EAC9B,EAEIoG,EAAqBC,KAAK,2BAA6BA,KAAK,4BAA8B,GAC9FD,EAAmBxC,QAAQmC,EAAqBjC,KAAK,KAAM,IAC3DsC,EAAmB/S,KAAO0S,EAAqBjC,KAAK,KAAMsC,EAAmB/S,KAAKyQ,KAAKsC,G,ICpFvF,IAAIE,EAAsBhH,EAAoBS,OAAE7R,EAAW,CAAC,KAAM,WAAa,OAAOoR,EAAoB,IAAM,GAChHgH,EAAsBhH,EAAoBS,EAAEuG,E", "sources": ["webpack://tab-kit-web/./src/App.vue?f316", "webpack://tab-kit-web/./src/App.vue", "webpack://tab-kit-web/./src/App.vue?7ccd", "webpack://tab-kit-web/./src/views/HomeView.vue?e8ad", "webpack://tab-kit-web/./src/views/HomeView.vue", "webpack://tab-kit-web/./src/views/HomeView.vue?1da1", "webpack://tab-kit-web/./src/router/index.ts", "webpack://tab-kit-web/./src/store/index.ts", "webpack://tab-kit-web/./src/utils/errorHandler.ts", "webpack://tab-kit-web/./src/main.ts", "webpack://tab-kit-web/./src/api/appApi.ts", "webpack://tab-kit-web/webpack/bootstrap", "webpack://tab-kit-web/webpack/runtime/chunk loaded", "webpack://tab-kit-web/webpack/runtime/compat get default export", "webpack://tab-kit-web/webpack/runtime/define property getters", "webpack://tab-kit-web/webpack/runtime/ensure chunk", "webpack://tab-kit-web/webpack/runtime/get javascript chunk filename", "webpack://tab-kit-web/webpack/runtime/get mini-css chunk filename", "webpack://tab-kit-web/webpack/runtime/global", "webpack://tab-kit-web/webpack/runtime/hasOwnProperty shorthand", "webpack://tab-kit-web/webpack/runtime/load script", "webpack://tab-kit-web/webpack/runtime/make namespace object", "webpack://tab-kit-web/webpack/runtime/publicPath", "webpack://tab-kit-web/webpack/runtime/css loading", "webpack://tab-kit-web/webpack/runtime/jsonp chunk loading", "webpack://tab-kit-web/webpack/startup"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, toDisplayString as _toDisplayString, resolveDynamicComponent as _resolveDynamicComponent, normalizeClass as _normalizeClass } from \"vue\"\nimport _imports_0 from '@/assets/logo.svg'\n\n\nconst _hoisted_1 = { id: \"app\" }\nconst _hoisted_2 = { class: \"menu-header\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"logo-container\"\n}\nconst _hoisted_4 = {\n  key: 1,\n  class: \"logo-container-collapsed\"\n}\nconst _hoisted_5 = { class: \"menu-items\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"menu-text\"\n}\nconst _hoisted_7 = [\"src\", \"alt\"]\nconst _hoisted_8 = {\n  key: 3,\n  class: \"menu-text\"\n}\nconst _hoisted_9 = { class: \"menu-bottom\" }\nconst _hoisted_10 = [\"src\", \"alt\"]\nconst _hoisted_11 = {\n  key: 3,\n  class: \"menu-text\"\n}\nconst _hoisted_12 = { class: \"menu-toggle-section\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_router_link = _resolveComponent(\"router-link\")!\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_router_view = _resolveComponent(\"router-view\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", {\n      class: _normalizeClass([\"sidebar\", { collapsed: _ctx.isMenuCollapsed }])\n    }, [\n      _createElementVNode(\"div\", _hoisted_2, [\n        (!_ctx.isMenuCollapsed)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n              _createVNode(_component_router_link, { to: \"/\" }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createElementVNode(\"img\", {\n                    src: _imports_0,\n                    alt: \"Logo\",\n                    class: \"logo\"\n                  }, null, -1)\n                ])),\n                _: 1,\n                __: [3]\n              }),\n              _cache[4] || (_cache[4] = _createElementVNode(\"span\", { class: \"app-name\" }, \"TabKit\", -1))\n            ]))\n          : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createVNode(_component_router_link, { to: \"/\" }, {\n                default: _withCtx(() => _cache[5] || (_cache[5] = [\n                  _createElementVNode(\"img\", {\n                    src: _imports_0,\n                    alt: \"Logo\",\n                    class: \"logo-small\"\n                  }, null, -1)\n                ])),\n                _: 1,\n                __: [5]\n              })\n            ]))\n      ]),\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_router_link, {\n          to: \"/\",\n          class: \"menu-item\",\n          \"active-class\": \"active\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_font_awesome_icon, {\n              icon: \"home\",\n              class: \"menu-icon\"\n            }),\n            (!_ctx.isMenuCollapsed)\n              ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \"主页\"))\n              : _createCommentVNode(\"\", true)\n          ]),\n          _: 1\n        }),\n        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.topMenuApps, (app) => {\n          return (_openBlock(), _createBlock(_resolveDynamicComponent(app.appType === _ctx.AppType.Internal ? 'router-link' : 'div'), {\n            key: app.id,\n            to: app.appType === _ctx.AppType.Internal ? app.appUrl : undefined,\n            class: _normalizeClass([\"menu-item\", { 'external-app': app.appType === _ctx.AppType.External }]),\n            \"active-class\": \"active\",\n            onClick: ($event: any) => (_ctx.handleMenuClick(app, $event))\n          }, {\n            default: _withCtx(() => [\n              (app.iconExists && _ctx.hasFileExtension(app.icon))\n                ? (_openBlock(), _createElementBlock(\"img\", {\n                    key: 0,\n                    src: _ctx.getAppIconUrl(app.id),\n                    alt: app.name,\n                    class: \"menu-icon-img\",\n                    onError: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.handleIconError && _ctx.handleIconError(...args)))\n                  }, null, 40, _hoisted_7))\n                : (app.icon && !_ctx.hasFileExtension(app.icon))\n                  ? (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                      key: 1,\n                      icon: app.icon,\n                      class: \"menu-icon\"\n                    }, null, 8, [\"icon\"]))\n                  : (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                      key: 2,\n                      icon: \"cube\",\n                      class: \"menu-icon\"\n                    })),\n              (!_ctx.isMenuCollapsed)\n                ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, _toDisplayString(app.name), 1))\n                : _createCommentVNode(\"\", true)\n            ]),\n            _: 2\n          }, 1032, [\"to\", \"class\", \"onClick\"]))\n        }), 128))\n      ]),\n      _createElementVNode(\"div\", _hoisted_9, [\n        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.bottomMenuApps, (app) => {\n          return (_openBlock(), _createBlock(_resolveDynamicComponent(app.appType === _ctx.AppType.Internal ? 'router-link' : 'div'), {\n            key: app.id,\n            to: app.appType === _ctx.AppType.Internal ? app.appUrl : undefined,\n            class: _normalizeClass([\"menu-item\", { 'external-app': app.appType === _ctx.AppType.External }]),\n            \"active-class\": \"active\",\n            onClick: ($event: any) => (_ctx.handleMenuClick(app, $event))\n          }, {\n            default: _withCtx(() => [\n              (app.iconExists && _ctx.hasFileExtension(app.icon))\n                ? (_openBlock(), _createElementBlock(\"img\", {\n                    key: 0,\n                    src: _ctx.getAppIconUrl(app.id),\n                    alt: app.name,\n                    class: \"menu-icon-img\",\n                    onError: _cache[1] || (_cache[1] = \n//@ts-ignore\n(...args) => (_ctx.handleIconError && _ctx.handleIconError(...args)))\n                  }, null, 40, _hoisted_10))\n                : (app.icon && !_ctx.hasFileExtension(app.icon))\n                  ? (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                      key: 1,\n                      icon: app.icon,\n                      class: \"menu-icon\"\n                    }, null, 8, [\"icon\"]))\n                  : (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                      key: 2,\n                      icon: \"cube\",\n                      class: \"menu-icon\"\n                    })),\n              (!_ctx.isMenuCollapsed)\n                ? (_openBlock(), _createElementBlock(\"span\", _hoisted_11, _toDisplayString(app.name), 1))\n                : _createCommentVNode(\"\", true)\n            ]),\n            _: 2\n          }, 1032, [\"to\", \"class\", \"onClick\"]))\n        }), 128)),\n        _createElementVNode(\"div\", _hoisted_12, [\n          _createElementVNode(\"div\", {\n            class: \"menu-toggle\",\n            onClick: _cache[2] || (_cache[2] = \n//@ts-ignore\n(...args) => (_ctx.toggleMenu && _ctx.toggleMenu(...args)))\n          }, [\n            _createVNode(_component_font_awesome_icon, {\n              icon: _ctx.isMenuCollapsed ? 'chevron-right' : 'chevron-left'\n            }, null, 8, [\"icon\"])\n          ])\n        ])\n      ])\n    ], 2),\n    _createElementVNode(\"div\", {\n      class: _normalizeClass([\"main-content\", { expanded: _ctx.isMenuCollapsed }])\n    }, [\n      _createVNode(_component_router_view)\n    ], 2)\n  ]))\n}", "<template>\n  <div id=\"app\">\n    <!-- 侧边菜单 -->\n    <div class=\"sidebar\" :class=\"{ collapsed: isMenuCollapsed }\">\n      <!-- 菜单头 -->\n      <div class=\"menu-header\">\n        <div class=\"logo-container\" v-if=\"!isMenuCollapsed\">\n          <router-link to=\"/\">\n            <img src=\"@/assets/logo.svg\" alt=\"Logo\" class=\"logo\" />\n          </router-link>\n          <span class=\"app-name\">TabKit</span>\n        </div>\n        <div class=\"logo-container-collapsed\" v-else>\n          <router-link to=\"/\">\n            <img src=\"@/assets/logo.svg\" alt=\"Logo\" class=\"logo-small\" />\n          </router-link>\n        </div>\n      </div>\n\n      <!-- 菜单项 -->\n      <div class=\"menu-items\">\n        <!-- 主页 -->\n        <router-link to=\"/\" class=\"menu-item\" active-class=\"active\">\n          <font-awesome-icon icon=\"home\" class=\"menu-icon\" />\n          <span v-if=\"!isMenuCollapsed\" class=\"menu-text\">主页</span>\n        </router-link>\n\n        <!-- 动态顶部菜单项 -->\n        <component\n          v-for=\"app in topMenuApps\"\n          :key=\"app.id\"\n          :is=\"app.appType === AppType.Internal ? 'router-link' : 'div'\"\n          :to=\"app.appType === AppType.Internal ? app.appUrl : undefined\"\n          class=\"menu-item\"\n          :class=\"{ 'external-app': app.appType === AppType.External }\"\n          active-class=\"active\"\n          @click=\"handleMenuClick(app, $event)\"\n        >\n          <!-- 图标：支持文件图标和前端图标 -->\n          <img\n            v-if=\"app.iconExists && hasFileExtension(app.icon)\"\n            :src=\"getAppIconUrl(app.id)\"\n            :alt=\"app.name\"\n            class=\"menu-icon-img\"\n            @error=\"handleIconError\"\n          />\n          <font-awesome-icon \n            v-else-if=\"app.icon && !hasFileExtension(app.icon)\"\n            :icon=\"app.icon\" \n            class=\"menu-icon\" \n          />\n          <font-awesome-icon \n            v-else\n            icon=\"cube\" \n            class=\"menu-icon\" \n          />\n          <span v-if=\"!isMenuCollapsed\" class=\"menu-text\">{{ app.name }}</span>\n        </component>\n      </div>\n\n      <!-- 底部菜单项 -->\n      <div class=\"menu-bottom\">\n        <!-- 动态底部菜单项 -->\n        <component\n          v-for=\"app in bottomMenuApps\"\n          :key=\"app.id\"\n          :is=\"app.appType === AppType.Internal ? 'router-link' : 'div'\"\n          :to=\"app.appType === AppType.Internal ? app.appUrl : undefined\"\n          class=\"menu-item\"\n          :class=\"{ 'external-app': app.appType === AppType.External }\"\n          active-class=\"active\"\n          @click=\"handleMenuClick(app, $event)\"\n        >\n          <!-- 图标：支持文件图标和前端图标 -->\n          <img\n            v-if=\"app.iconExists && hasFileExtension(app.icon)\"\n            :src=\"getAppIconUrl(app.id)\"\n            :alt=\"app.name\"\n            class=\"menu-icon-img\"\n            @error=\"handleIconError\"\n          />\n          <font-awesome-icon \n            v-else-if=\"app.icon && !hasFileExtension(app.icon)\"\n            :icon=\"app.icon\" \n            class=\"menu-icon\" \n          />\n          <font-awesome-icon \n            v-else\n            icon=\"cube\" \n            class=\"menu-icon\" \n          />\n          <span v-if=\"!isMenuCollapsed\" class=\"menu-text\">{{ app.name }}</span>\n        </component>\n\n        <!-- 菜单切换按钮 -->\n        <div class=\"menu-toggle-section\">\n          <div class=\"menu-toggle\" @click=\"toggleMenu\">\n            <font-awesome-icon :icon=\"isMenuCollapsed ? 'chevron-right' : 'chevron-left'\" />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主内容区域 -->\n    <div class=\"main-content\" :class=\"{ expanded: isMenuCollapsed }\">\n      <router-view />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from 'vue';\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\nimport { appApi, AppEntry, AppType } from '@/api/appApi';\nimport { ElMessageBox } from 'element-plus';\n\nexport default defineComponent({\n  name: 'App',\n  components: {\n    FontAwesomeIcon,\n  },\n  setup() {\n    const isMenuCollapsed = ref(false);\n    const menuApps = ref<AppEntry[]>([]);\n\n    const toggleMenu = () => {\n      isMenuCollapsed.value = !isMenuCollapsed.value;\n    };\n\n    // 计算属性：顶部菜单应用（包含内部和外部应用）\n    const topMenuApps = computed(() => {\n      return menuApps.value\n        .filter(app => app.showInTopMenu)\n        .sort((a, b) => a.displayOrder - b.displayOrder);\n    });\n\n    // 计算属性：底部菜单应用（包含内部和外部应用）\n    const bottomMenuApps = computed(() => {\n      return menuApps.value\n        .filter(app => app.showInBottomMenu)\n        .sort((a, b) => a.displayOrder - b.displayOrder);\n    });\n\n    // 加载菜单应用配置\n    const loadMenuApps = async () => {\n      try {\n        const response = await appApi.appConfig.getList();\n        menuApps.value = response.data;\n      } catch (error) {\n        console.error('加载菜单应用配置失败:', error);\n      }\n    };\n\n    // 处理菜单项点击\n    const handleMenuClick = async (app: AppEntry, event: Event) => {\n      if (app.appType === AppType.External) {\n        // 外部应用：阻止默认导航行为，直接启动外部程序\n        event.preventDefault();\n        await launchApp(app);\n      }\n      // 内部应用：允许默认的路由导航\n    };\n\n    // 启动应用程序\n    const launchApp = async (app: AppEntry) => {\n      if (!app.exeExists) {\n        // 这里可以使用 Element Plus 的消息提示，如果可用的话\n        console.error(`应用 \"${app.name}\" 的可执行文件不存在`);\n        return;\n      }\n\n      await ElMessageBox.confirm(`是否启动 \"${app.name}\" ?`, '确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      });\n\n      try {\n        await appApi.appConfig.launch({ appId: app.id });\n        console.log(`正在启动 \"${app.name}\"`);\n      } catch (error: any) {\n        console.error('启动应用程序失败:', error);\n        console.error(error.response?.data?.message || `启动 \"${app.name}\" 失败`);\n      }\n    };\n\n    // 判断是否为文件图标（有文件扩展名）\n    const hasFileExtension = (filename: string): boolean => {\n      if (!filename) return false;\n      const dotIndex = filename.lastIndexOf('.');\n      return dotIndex > 0 && dotIndex < filename.length - 1;\n    };\n\n    // 获取应用图标 URL\n    const getAppIconUrl = (appId: string): string => {\n      return appApi.appConfig.getIconUrl(appId);\n    };\n\n    // 处理图标加载错误\n    const handleIconError = (event: Event) => {\n      const target = event.target as HTMLImageElement;\n      target.style.display = 'none';\n    };\n\n    onMounted(() => {\n      loadMenuApps();\n    });\n\n    return {\n      isMenuCollapsed,\n      toggleMenu,\n      topMenuApps,\n      bottomMenuApps,\n      handleMenuClick,\n      hasFileExtension,\n      getAppIconUrl,\n      handleIconError,\n      AppType,\n    };\n  },\n});\n</script>\n\n<style lang=\"scss\">\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  height: 100vh;\n  display: flex;\n  width: 100vw;\n}\n\n.sidebar {\n  width: 200px;\n  background-color: white;\n  color: #333;\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  position: relative;\n  border-right: 1px solid #e4e7ed;\n\n  &.collapsed {\n    width: 60px;\n  }\n\n  .menu-header {\n    height: 60px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0 15px;\n    border-bottom: 1px solid #e4e7ed;\n    background-color: white;\n\n    .logo-container {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n\n      .logo {\n        width: 36px;\n        height: 36px;\n      }\n\n      .app-name {\n        font-size: 18px;\n        font-weight: 600;\n        color: #333;\n        margin-right: 40px;\n      }\n    }\n\n    .logo-container-collapsed {\n      display: flex;\n      justify-content: center;\n      width: 100%;\n\n      .logo-small {\n        width: 32px;\n        height: 32px;\n      }\n    }\n  }\n\n  .menu-toggle-section {\n    padding: 8px 12px;\n    border-top: 1px solid #e4e7ed;\n    height: 44px;\n\n    .menu-toggle {\n      width: 100%;\n      height: 36px;\n      background-color: #f5f7fa;\n      border-radius: 6px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      font-size: 14px;\n      color: #666;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #e4e7ed;\n        color: #333;\n      }\n    }\n  }\n\n  &.collapsed .menu-toggle-section {\n    padding: 8px 6px;\n\n    .menu-toggle {\n      height: 32px;\n      border-radius: 4px;\n    }\n  }\n\n  .menu-items {\n    flex: 1;\n    padding-top: 10px;\n  }\n\n  .menu-bottom {\n    padding-bottom: 10px;\n  }\n\n  .menu-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 20px;\n    color: #666;\n    text-decoration: none;\n    transition: all 0.3s ease;\n    border-left: 3px solid transparent;\n    margin: 2px 8px;\n    border-radius: 6px;\n    height: 44px;\n\n    &:hover {\n      background-color: #f5f7fa;\n      color: #333;\n    }\n\n    &.active {\n      background-color: #e6f7ff;\n      color: var(--el-color-primary);\n      border-left-color: var(--el-color-primary);\n    }\n\n    // 外部应用样式：可点击但不会激活\n    &.external-app {\n      cursor: pointer;\n      \n      &:hover {\n        background-color: #f5f7fa;\n        color: #333;\n      }\n      \n      // 外部应用不应用激活样式\n      &.active {\n        background-color: #f5f7fa;\n        color: #333;\n        border-left-color: transparent;\n      }\n    }\n\n    .menu-icon {\n      font-size: 16px;\n      width: 20px;\n      text-align: center;\n    }\n\n    .menu-icon-img {\n      width: 20px;\n      height: 20px;\n      object-fit: contain;\n      border-radius: 2px;\n    }\n\n    .menu-text {\n      margin-left: 12px;\n      font-size: 14px;\n      white-space: nowrap;\n      overflow: hidden;\n      font-weight: 500;\n    }\n  }\n\n  &.collapsed .menu-item {\n    justify-content: center;\n    padding: 12px 10px;\n    margin: 2px 4px;\n\n    .menu-icon {\n      margin: 0;\n    }\n  }\n}\n\n.main-content {\n  flex: 1;\n  background-color: var(--el-fill-color-base);\n  overflow-y: auto;\n  transition: margin-left 0.3s ease;\n\n  &.expanded {\n    margin-left: 0;\n  }\n}\n</style>\n", "import { render } from \"./App.vue?vue&type=template&id=6e94b741&ts=true\"\nimport script from \"./App.vue?vue&type=script&lang=ts\"\nexport * from \"./App.vue?vue&type=script&lang=ts\"\n\nimport \"./App.vue?vue&type=style&index=0&id=6e94b741&lang=scss\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode, withCtx as _withCtx, createBlock as _createBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"home\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_3 = {\n  key: 1,\n  class: \"tools-grid\"\n}\nconst _hoisted_4 = {\n  key: 0,\n  class: \"external-app-badge\"\n}\nconst _hoisted_5 = { class: \"tool-icon\" }\nconst _hoisted_6 = [\"src\", \"alt\"]\nconst _hoisted_7 = {\n  key: 1,\n  class: \"tool-features\"\n}\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"page-header\" }, [\n      _createElementVNode(\"h1\", null, \"TabKit\"),\n      _createElementVNode(\"p\", null, \"TabKit 中集成了多种实用工具\")\n    ], -1)),\n    (_ctx.loading)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n          _createVNode(_component_el_skeleton, {\n            rows: 3,\n            animated: \"\"\n          })\n        ]))\n      : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n          (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.homeDisplayApps, (app) => {\n            return (_openBlock(), _createBlock(_component_el_card, {\n              key: app.id,\n              class: _normalizeClass([\"tool-card external-app-card\", {\n        'app-unavailable': !app.exeExists\n      }]),\n              shadow: \"hover\",\n              onClick: ($event: any) => (_ctx.handleAppClick(app))\n            }, {\n              default: _withCtx(() => [\n                (!app.exeExists && app.appType === _ctx.AppType.External)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n                      _createVNode(_component_el_tag, {\n                        size: \"small\",\n                        type: \"danger\"\n                      }, {\n                        default: _withCtx(() => _cache[1] || (_cache[1] = [\n                          _createTextVNode(\"不可用\")\n                        ])),\n                        _: 1,\n                        __: [1]\n                      })\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                _createElementVNode(\"div\", _hoisted_5, [\n                  (app.iconExists && _ctx.hasFileExtension(app.icon))\n                    ? (_openBlock(), _createElementBlock(\"img\", {\n                        key: 0,\n                        src: _ctx.getAppIconUrl(app.id),\n                        alt: app.name,\n                        class: \"app-icon\",\n                        onError: _cache[0] || (_cache[0] = ($event: any) => (_ctx.handleIconError($event)))\n                      }, null, 40, _hoisted_6))\n                    : (app.icon && !_ctx.hasFileExtension(app.icon))\n                      ? (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                          key: 1,\n                          icon: app.icon,\n                          class: \"default-app-icon\"\n                        }, null, 8, [\"icon\"]))\n                      : (_openBlock(), _createBlock(_component_font_awesome_icon, {\n                          key: 2,\n                          icon: \"cube\",\n                          class: \"default-app-icon\"\n                        }))\n                ]),\n                _createElementVNode(\"h3\", null, _toDisplayString(app.name || 'Unknown App'), 1),\n                _createElementVNode(\"p\", null, _toDisplayString(app.description || '无描述信息'), 1),\n                (app.tags && app.tags.length > 0)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [\n                      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(app.tags, (tag) => {\n                        return (_openBlock(), _createBlock(_component_el_tag, {\n                          key: tag,\n                          size: \"small\",\n                          type: _ctx.getTagColor(tag)\n                        }, {\n                          default: _withCtx(() => [\n                            _createTextVNode(_toDisplayString(tag), 1)\n                          ]),\n                          _: 2\n                        }, 1032, [\"type\"]))\n                      }), 128))\n                    ]))\n                  : _createCommentVNode(\"\", true)\n              ]),\n              _: 2\n            }, 1032, [\"class\", \"onClick\"]))\n          }), 128))\n        ]))\n  ]))\n}", "<template>\n  <div class=\"home\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1>TabKit</h1>\n      <p>TabKit 中集成了多种实用工具</p>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n    </div>\n\n    <!-- 功能卡片网格 -->\n    <div v-else class=\"tools-grid\">\n      <!-- 配置的应用程序卡片 -->\n      <el-card v-for=\"app in homeDisplayApps\" :key=\"app.id\" class=\"tool-card external-app-card\" :class=\"{\n        'app-unavailable': !app.exeExists\n      }\" shadow=\"hover\" @click=\"handleAppClick(app)\">\n        <!-- 不可用状态标识 -->\n        <div v-if=\"!app.exeExists && app.appType === AppType.External\" class=\"external-app-badge\">\n          <el-tag size=\"small\" type=\"danger\">不可用</el-tag>\n        </div>\n\n        <!-- 应用图标 -->\n        <div class=\"tool-icon\">\n          <img v-if=\"app.iconExists && hasFileExtension(app.icon)\" :src=\"getAppIconUrl(app.id)\" :alt=\"app.name\"\n            class=\"app-icon\" @error=\"handleIconError($event)\" />\n          <font-awesome-icon v-else-if=\"app.icon && !hasFileExtension(app.icon)\" :icon=\"app.icon\"\n            class=\"default-app-icon\" />\n          <font-awesome-icon v-else icon=\"cube\" class=\"default-app-icon\" />\n        </div>\n\n        <!-- 应用信息 -->\n        <h3>{{ app.name || 'Unknown App' }}</h3>\n        <p>{{ app.description || '无描述信息' }}</p>\n\n        <!-- 应用标签 -->\n        <div v-if=\"app.tags && app.tags.length > 0\" class=\"tool-features\">\n          <el-tag v-for=\"tag in app.tags\" :key=\"tag\" size=\"small\" :type=\"getTagColor(tag)\">\n            {{ tag }}\n          </el-tag>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, computed } from \"vue\";\nimport { useRouter } from \"vue-router\";\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\nimport { appApi, AppEntry, AppType } from \"@/api/appApi\";\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\nexport default defineComponent({\n  name: \"HomeView\",\n  components: {\n    FontAwesomeIcon,\n  },\n  setup() {\n    const router = useRouter();\n    const appEntries = ref<AppEntry[]>([]);\n    const loading = ref(false);\n\n    const navigateToTool = (path: string) => {\n      router.push(path);\n    };\n\n    // 计算属性：过滤需要在主页显示的应用\n    const homeDisplayApps = computed(() => {\n      return appEntries.value\n        .filter(app => app.showInHomeCard)\n        .sort((a, b) => a.displayOrder - b.displayOrder);\n    });\n\n    // 生成标签颜色的哈希函数，映射到 Element Plus 预定义类型\n    const getTagColor = (tag: string): string => {\n      const colors = ['', 'success', 'warning', 'info'];\n      let hash = 0;\n      for (let i = 0; i < tag.length; i++) {\n        hash = tag.charCodeAt(i) + ((hash << 5) - hash);\n      }\n      return colors[Math.abs(hash) % colors.length];\n    };\n\n    // 获取应用图标 URL\n    const getAppIconUrl = (appId: string): string => {\n      return appApi.appConfig.getIconUrl(appId);\n    };\n\n    // 处理图标加载错误\n    const handleIconError = (event: Event) => {\n      const target = event.target as HTMLImageElement;\n      target.style.display = 'none';\n      // 可以在这里添加显示默认图标的逻辑\n    };\n\n    // 检查是否有文件扩展名\n    const hasFileExtension = (filename: string): boolean => {\n      if (!filename) return false;\n      const dotIndex = filename.lastIndexOf('.');\n      return dotIndex > 0 && dotIndex < filename.length - 1;\n    };\n\n    // 加载应用程序列表\n    const loadAppEntries = async () => {\n      try {\n        loading.value = true;\n        const response = await appApi.appConfig.getList();\n        appEntries.value = response.data;\n      } catch (error) {\n        console.error('加载应用程序失败:', error);\n        // 静默处理错误，不显示错误消息，因为可能是没有配置应用程序\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 处理应用点击事件\n    const handleAppClick = async (app: AppEntry) => {\n      if (app.appType === AppType.Internal) {\n        // 内部应用：跳转到指定路由\n        if (app.appUrl) {\n          navigateToTool(app.appUrl);\n        }\n      } else {\n        // 外部应用：启动外部程序\n        await launchApp(app);\n      }\n    };\n\n    // 启动应用程序\n    const launchApp = async (app: AppEntry) => {\n      if (!app.exeExists) {\n        ElMessage.error(`应用 \"${app.name}\" 的可执行文件不存在`);\n        return;\n      }\n\n      await ElMessageBox.confirm(`是否启动 \"${app.name}\" ?`, '确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      });\n\n      try {\n        await appApi.appConfig.launch({ appId: app.id });\n        ElMessage.success(`正在启动 \"${app.name}\"...`);\n      } catch (error: any) {\n        console.error('启动应用失败:', error);\n        ElMessage.error(error.response?.data?.message || `启动 \"${app.name}\" 失败`);\n      }\n    };\n\n    onMounted(() => {\n      loadAppEntries();\n    });\n\n    return {\n      navigateToTool,\n      appEntries,\n      homeDisplayApps,\n      loading,\n      getTagColor,\n      handleAppClick,\n      launchApp,\n      getAppIconUrl,\n      handleIconError,\n      hasFileExtension,\n      AppType,\n    };\n  },\n});\n</script>\n\n<style scoped>\n.home {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  text-align: center;\n  margin-top: 40px;\n  margin-bottom: 40px;\n}\n\n.page-header h1 {\n  font-size: 2.5rem;\n  color: var(--el-text-color-primary);\n  margin-bottom: 10px;\n}\n\n.page-header p {\n  font-size: 1.1rem;\n  color: var(--el-text-color-regular);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 16px;\n  margin-bottom: 40px;\n}\n\n.tool-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n  padding: 16px;\n  text-align: center;\n  min-height: 220px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.tool-card:hover {\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n.tool-card.coming-soon {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n.tool-card.coming-soon:hover {\n  transform: none;\n  box-shadow: none;\n}\n\n.tool-icon {\n  font-size: 2rem;\n  color: var(--el-color-primary);\n  margin-bottom: 16px;\n\n  .logo-icon {\n    width: 2rem;\n    height: 2rem;\n  }\n}\n\n.tool-card h3 {\n  font-size: 1.3rem;\n  color: var(--el-text-color-primary);\n  margin-bottom: 12px;\n}\n\n.tool-card p {\n  color: var(--el-text-color-regular);\n  line-height: 1.5;\n  margin-bottom: 16px;\n  flex-grow: 1;\n  font-size: 0.9rem;\n}\n\n.tool-features {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n/* External Apps 相关样式 */\n\n.external-app-card {\n  position: relative;\n}\n\n.external-app-badge {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1;\n}\n\n.external-app-card.app-unavailable {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.external-app-card.app-unavailable:hover {\n  transform: none;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.app-icon {\n  width: 2rem;\n  height: 2rem;\n  object-fit: contain;\n}\n\n.default-app-icon {\n  font-size: 2rem;\n  color: var(--el-color-primary);\n}\n\n.app-status {\n  margin-bottom: 10px;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  padding: 20px;\n}\n\n@media (max-width: 768px) {\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .page-header h1 {\n    font-size: 2rem;\n  }\n}\n</style>\n", "import { render } from \"./HomeView.vue?vue&type=template&id=861d530e&scoped=true&ts=true\"\nimport script from \"./HomeView.vue?vue&type=script&lang=ts\"\nexport * from \"./HomeView.vue?vue&type=script&lang=ts\"\n\nimport \"./HomeView.vue?vue&type=style&index=0&id=861d530e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-861d530e\"]])\n\nexport default __exports__", "import { createRouter, createWebHistory, RouteRecordRaw } from \"vue-router\";\nimport HomeView from \"../views/HomeView.vue\";\n\nconst routes: Array<RouteRecordRaw> = [\n  {\n    path: \"/\",\n    name: \"home\",\n    component: HomeView,\n  },\n  {\n    path: \"/log-converter\",\n    name: \"log-converter\",\n    component: () =>\n      import(/* webpackChunkName: \"log-converter\" */ \"../views/LogConverterView.vue\"),\n  },\n  {\n    path: \"/parameter-tool\",\n    name: \"parameter-tool\",\n    component: () =>\n      import(/* webpackChunkName: \"parameter-tool\" */ \"../views/ParameterToolView.vue\"),\n  },\n  {\n    path: \"/about\",\n    name: \"about\",\n    component: () =>\n      import(/* webpackChunkName: \"about\" */ \"../views/AboutView.vue\"),\n  },\n];\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes,\n});\n\nexport default router;\n", "import { createStore } from \"vuex\";\n\nexport default createStore({\n  state: {},\n  getters: {},\n  mutations: {},\n  actions: {},\n  modules: {},\n});\n", "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\n\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport './styles/element-variables.css' // 需要创建这个文件来自定义主题\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt, faBook,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faCommentAlt, faCube,\n  faA, faB, faC, faD, faE, faF, faG,\n  faH, faI, faJ, faK, faL, faM, faN,\n  faO, faP, faQ, faR, faS, faT,\n  faU, faV, faW, faX, faY, faZ,\n  fa0, fa1, fa2, fa3, fa4, fa5, fa6, fa7, fa8, fa9\n} from '@fortawesome/free-solid-svg-icons'\n\nimport { faGithub, faTeamspeak } from '@fortawesome/free-brands-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt, faBook,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faGithub, faTeamspeak, faCommentAlt, faCube,\n  faA, faB, faC, faD, faE, faF, faG,\n  faH, faI, faJ, faK, faL, faM, faN,\n  faO, faP, faQ, faR, faS, faT,\n  faU, faV, faW, faX, faY, faZ,\n  fa0, fa1, fa2, fa3, fa4, fa5, fa6, fa7, fa8, fa9\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "import axios, { AxiosResponse } from 'axios';\r\n\r\n// 定义错误数据结构\r\nexport interface ErrorData {\r\n  message: string;\r\n  stack?: string;\r\n  url: string;\r\n  type?: string;\r\n  vueHookInfo?: string; \r\n  codeInfo?: string;\r\n}\r\n\r\n// 定义应用信息接口\r\nexport interface AppInfo {\r\n  dataFolder: string;\r\n  logFolder: string;\r\n}\r\n\r\n// 定义测试模型接口\r\nexport interface TestMode {\r\n  name: string;\r\n}\r\n\r\n// 数据日志转换相关接口\r\nexport enum DataLogFormat {\r\n  Unknown = 0,\r\n  Asc = 1,\r\n  Blf = 2\r\n}\r\n\r\n\r\n\r\nexport interface DataLogProcessRequest {\r\n  sourceFilePath: string;\r\n  targetFormat: DataLogFormat;\r\n  enableSplit: boolean;\r\n  splitFileCount: number;\r\n}\r\n\r\nexport interface FileProgress {\r\n  fileName: string;\r\n  filePath: string;\r\n  status: ProcessStatus;\r\n  progressPercentage: number;\r\n  errorMessage?: string;\r\n}\r\n\r\nexport interface ProcessProgress {\r\n  taskId: string;\r\n  overallProgressPercentage: number;\r\n  currentOperation: string;\r\n  isCompleted: boolean;\r\n  errorMessage?: string;\r\n  fileProgresses: FileProgress[];\r\n}\r\n\r\nexport enum ProcessStatus {\r\n  Pending = \"Pending\",\r\n  Processing = \"Processing\",\r\n  Completed = \"Completed\",\r\n  Failed = \"Failed\",\r\n  Cancelled = \"Cancelled\"\r\n}\r\n\r\n// App Config 相关接口\r\n\r\nexport enum AppType {\r\n  External = 'External',\r\n  Internal = 'Internal'\r\n}\r\n\r\nexport interface AppEntry {\r\n  id: string;\r\n  appType: AppType;\r\n  exePath: string;\r\n  appUrl: string;\r\n  name: string;\r\n  description: string;\r\n  iconPath: string;\r\n  tags: string[];\r\n  showInTopMenu: boolean;\r\n  showInBottomMenu: boolean;\r\n  displayOrder: number;\r\n  showInHomeCard: boolean;\r\n  icon: string;\r\n  exeExists: boolean;\r\n  iconExists: boolean;\r\n  fullExePath: string;\r\n  workingDirectory: string;\r\n}\r\n\r\nexport interface LaunchAppRequest {\r\n  appId: string;\r\n}\r\n\r\n// CIN 参数工具相关接口\r\nexport interface CinTemplate {\r\n  id: string;\r\n  path: string;\r\n  name: string;\r\n  category: string;\r\n  description: string;\r\n  fullPath: string;\r\n  fileExists: boolean;\r\n}\r\n\r\nexport interface CaplVariable {\r\n  name: string;\r\n  type: string;\r\n  isConst: boolean;\r\n  isArray: boolean;\r\n  arraySize: string;\r\n  value: string;\r\n  originalDeclaration: string;\r\n  lineNumber: number;\r\n  description?: string;\r\n}\r\n\r\nexport interface CinParameterParseRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n}\r\n\r\nexport interface CinParameterParseResponse {\r\n  sourceFilePath: string;\r\n  variables: CaplVariable[];\r\n}\r\n\r\nexport interface CinParameterRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n  parameterValues: { [key: string]: string };\r\n}\r\n\r\n// 源文件管理相关接口\r\nexport enum SourceFileType {\r\n  Arxml = 'Arxml',\r\n  Sddb = 'Sddb',\r\n  Ldf = 'Ldf'\r\n}\r\n\r\nexport enum SourceFileStatus {\r\n  Pending = 'Pending',\r\n  Parsing = 'Parsing',\r\n  Parsed = 'Parsed',\r\n  Error = 'Error'\r\n}\r\n\r\nexport enum ParamType {\r\n  String = 'String',\r\n  Integer = 'Integer',\r\n  Double = 'Double',\r\n  Boolean = 'Boolean',\r\n  Json = 'Json',\r\n  Array = 'Array',\r\n  Object = 'Object'\r\n}\r\n\r\nexport interface SourceFile {\r\n  id: string;\r\n  path: string;\r\n  fileName: string;\r\n  fileType: SourceFileType;\r\n  status: SourceFileStatus;\r\n  parsedParams: ParsedParam[];\r\n  addTime: Date;\r\n  errorMessage: string;\r\n}\r\n\r\nexport interface ParsedParam {\r\n  ecuName: string;\r\n  name: string;\r\n  value: any;\r\n  paramType: ParamType;\r\n  source: string;\r\n  description: string;\r\n}\r\n\r\nexport interface AddSourceFilesRequest {\r\n  filePaths: string[];\r\n}\r\n\r\nexport interface ParseSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface RemoveSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface UserFileHistory {\r\n  lastSelectedPaths: string[];\r\n  lastUpdateTime: Date;\r\n}\r\n\r\nexport interface CinParameterProcessResponse {\r\n  outputFilePath: string;\r\n}\r\n\r\n\r\n\r\nconst BASE_URL = '/api/app'\r\nconst DATALOG_BASE_URL = '/api/DataLogConvert'\r\nconst EXPLORER_BASE_URL = '/api/explorer'\r\nconst APP_CONFIG_BASE_URL = '/api/AppConfig'\r\nconst CIN_PARAMETER_BASE_URL = '/api/CinParameter'\r\n\r\nexport const appApi = {\r\n  // 获取应用信息\r\n  getAppInfo(): Promise<AxiosResponse<AppInfo>> {\r\n    return axios.get(`${BASE_URL}/appInfo`);\r\n  },\r\n\r\n  // 记录错误日志\r\n  logError: (errorData: ErrorData) => {\r\n    return axios.post(`${BASE_URL}/logError`, errorData);\r\n  },\r\n\r\n  // 退出应用程序\r\n  exit: () => {\r\n    return axios.post(`${BASE_URL}/exit`);\r\n  },\r\n\r\n  // 获取测试模型\r\n  getTestModel(): Promise<AxiosResponse<TestMode>> {\r\n    return axios.get(`api/test/model`);\r\n  },\r\n\r\n  // 数据日志转换相关接口\r\n  dataLogConvert: {\r\n    // 选择文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 开始处理\r\n    startProcess(request: DataLogProcessRequest): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/start`, request);\r\n    },\r\n\r\n    // 获取进度\r\n    getProgress(taskId: string): Promise<AxiosResponse<ProcessProgress>> {\r\n      return axios.get(`${DATALOG_BASE_URL}/progress?taskId=${taskId}`);\r\n    },\r\n\r\n    // 取消处理\r\n    cancelProcess(taskId: string): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/cancel`, null, { params: { taskId } });\r\n    }\r\n  },\r\n\r\n  // 文件资源管理器相关接口\r\n  explorer: {\r\n    // 选择文件夹\r\n    selectFolder(): Promise<AxiosResponse<string>> {\r\n      return axios.get(`${EXPLORER_BASE_URL}/select-folder`);\r\n    },\r\n\r\n    // 在资源管理器中显示文件\r\n    openExplorer(path: string): Promise<AxiosResponse<void>> {\r\n      return axios.get(`${EXPLORER_BASE_URL}/open-explorer`, { params: { path } });\r\n    },\r\n\r\n    // 打开进程\r\n    startProcess(path: string): Promise<AxiosResponse<void>> {\r\n      return axios.get(`${EXPLORER_BASE_URL}/start-process`, { params: { path } });\r\n    }\r\n  },\r\n\r\n  // 应用配置相关接口\r\n  appConfig: {\r\n    // 获取应用列表\r\n    getList(): Promise<AxiosResponse<AppEntry[]>> {\r\n      return axios.get(`${APP_CONFIG_BASE_URL}/list`);\r\n    },\r\n\r\n    // 启动应用程序\r\n    launch(request: LaunchAppRequest): Promise<AxiosResponse<{ success: boolean; message: string }>> {\r\n      return axios.post(`${APP_CONFIG_BASE_URL}/launch`, request);\r\n    },\r\n\r\n    // 获取应用图标\r\n    getIconUrl(appId: string): string {\r\n      return `${APP_CONFIG_BASE_URL}/icon?appId=${appId}`;\r\n    }\r\n  },\r\n\r\n  // CIN 参数工具相关接口\r\n  cinParameter: {\r\n    // 获取 CIN 模板列表\r\n    getTemplates(): Promise<AxiosResponse<CinTemplate[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/templates`);\r\n    },\r\n\r\n    // 选择 CIN 文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 解析 CIN 文件参数\r\n    parseFile(request: CinParameterParseRequest): Promise<AxiosResponse<CinParameterParseResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse`, request);\r\n    },\r\n\r\n    // 处理 CIN 文件参数替换\r\n    processFile(request: CinParameterRequest): Promise<AxiosResponse<CinParameterProcessResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/process`, request);\r\n    },\r\n\r\n    // 源文件管理相关接口\r\n\r\n    // 选择源文件\r\n    selectSourceFiles(): Promise<AxiosResponse<string[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-source-files`);\r\n    },\r\n\r\n    // 添加源文件\r\n    addSourceFiles(request: AddSourceFilesRequest): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/add-source-files`, request);\r\n    },\r\n\r\n    // 获取所有源文件\r\n    getSourceFiles(): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/source-files`);\r\n    },\r\n\r\n    // 解析源文件参数\r\n    parseSourceFile(request: ParseSourceFileRequest): Promise<AxiosResponse<SourceFile>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse-source-file`, request);\r\n    },\r\n\r\n    // 移除源文件\r\n    removeSourceFile(request: RemoveSourceFileRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/remove-source-file`, request);\r\n    },\r\n\r\n    // 清空所有源文件\r\n    clearSourceFiles(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-source-files`);\r\n    },\r\n\r\n    // 获取文件选择历史\r\n    getFileHistory(): Promise<AxiosResponse<UserFileHistory>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/file-history`);\r\n    },\r\n\r\n    // 清空文件选择历史\r\n    clearFileHistory(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-file-history`);\r\n    }\r\n  }\r\n}\r\n\r\nexport default appApi\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + {\"488\":\"log-converter\",\"594\":\"about\",\"771\":\"parameter-tool\"}[chunkId] + \".\" + {\"488\":\"4e7d7f7f\",\"594\":\"c65a2bc9\",\"771\":\"a2113c95\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + {\"488\":\"log-converter\",\"594\":\"about\",\"771\":\"parameter-tool\"}[chunkId] + \".\" + {\"488\":\"17cf53fa\",\"594\":\"cc0d651e\",\"771\":\"9b37d0cc\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"tab-kit-web:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = function(chunkId, fullhref, oldTag, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"488\":1,\"594\":1,\"771\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunktab_kit_web\"] = self[\"webpackChunktab_kit_web\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(774); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["_hoisted_1", "id", "_hoisted_2", "class", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_router_link", "_resolveComponent", "_component_font_awesome_icon", "_component_router_view", "_openBlock", "_createElementBlock", "_createElementVNode", "_normalizeClass", "collapsed", "isMenuCollapsed", "_createVNode", "to", "default", "_withCtx", "src", "_imports_0", "alt", "_", "__", "icon", "_createCommentVNode", "_Fragment", "_renderList", "topMenuApps", "app", "_createBlock", "_resolveDynamicComponent", "appType", "AppType", "Internal", "appUrl", "undefined", "External", "onClick", "$event", "handleMenuClick", "iconExists", "hasFileExtension", "getAppIconUrl", "name", "onError", "args", "handleIconError", "_toDisplayString", "bottomMenuApps", "toggleMenu", "expanded", "defineComponent", "components", "FontAwesomeIcon", "setup", "ref", "menuApps", "value", "computed", "filter", "showInTopMenu", "sort", "a", "b", "displayOrder", "showInBottomMenu", "loadMenuApps", "async", "response", "appApi", "appConfig", "getList", "data", "error", "console", "event", "preventDefault", "launchApp", "exeExists", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "launch", "appId", "log", "message", "filename", "dotIndex", "lastIndexOf", "length", "getIconUrl", "target", "style", "display", "onMounted", "__exports__", "_component_el_skeleton", "_component_el_tag", "_component_el_card", "loading", "rows", "animated", "homeDisplayApps", "shadow", "handleAppClick", "size", "_createTextVNode", "description", "tags", "tag", "getTagColor", "router", "useRouter", "appEntries", "navigateToTool", "path", "push", "showInHomeCard", "colors", "hash", "i", "charCodeAt", "Math", "abs", "loadAppEntries", "ElMessage", "success", "routes", "component", "HomeView", "createRouter", "history", "createWebHistory", "process", "createStore", "state", "getters", "mutations", "actions", "modules", "formatErrorMessage", "errorData", "errorMessages", "exceptionMessage", "currentException", "innerException", "join", "showDetailedError", "errorMessage", "alert", "dangerouslyUseHTMLString", "closeOnClickModal", "closeOnPressEscape", "showClose", "isUserCanceled", "errorCode", "setup<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "axios", "interceptors", "use", "info", "Promise", "reject", "library", "add", "faCogs", "faFolderOpen", "faPlus", "faFileAlt", "faBook", "faHistory", "faTrashCan", "faFileExcel", "faClock", "faFolder", "faChartBar", "faProjectDiagram", "faClockRotateLeft", "faFileCircleExclamation", "faAngleDown", "faAngleUp", "faExpand", "faCompress", "faUpRightAndDownLeftFromCenter", "faDownLeftAndUpRightToCenter", "faHome", "faExchangeAlt", "faInfoCircle", "faChevronLeft", "faChevronRight", "faEye", "faPlay", "faStop", "faRefresh", "faSearch", "faDownload", "faTrash", "faCode", "faEnvelope", "faGlobe", "fa<PERSON><PERSON><PERSON>", "faTeamspeak", "faCommentAlt", "faCube", "faA", "faB", "faC", "faD", "faE", "faF", "faG", "faH", "faI", "faJ", "faK", "faL", "faM", "faN", "faO", "faP", "faQ", "faR", "faS", "faT", "faU", "faV", "faW", "faX", "faY", "faZ", "fa0", "fa1", "fa2", "fa3", "fa4", "fa5", "fa6", "fa7", "fa8", "fa9", "createApp", "App", "Object", "entries", "ElementPlusIconsVue", "store", "ElementPlus", "locale", "zhCn", "mount", "config", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "Error", "String", "stack", "vueHookInfo", "url", "window", "location", "href", "logError", "catch", "sendError", "addEventListener", "reason", "codeInfo", "lineno", "colno", "DataLogFormat", "ProcessStatus", "SourceFileType", "SourceFileStatus", "ParamType", "BASE_URL", "DATALOG_BASE_URL", "EXPLORER_BASE_URL", "APP_CONFIG_BASE_URL", "CIN_PARAMETER_BASE_URL", "getAppInfo", "get", "post", "exit", "getTestModel", "dataLogConvert", "selectFile", "startProcess", "request", "getProgress", "taskId", "cancelProcess", "params", "explorer", "selectFolder", "openExplorer", "cinParameter", "getTemplates", "parseFile", "processFile", "selectSourceFiles", "addSourceFiles", "getSourceFiles", "parseSourceFile", "removeSourceFile", "clearSourceFiles", "getFileHistory", "clearFileHistory", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "keys", "every", "splice", "r", "n", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "f", "e", "chunkId", "all", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "this", "Function", "obj", "prop", "prototype", "hasOwnProperty", "inProgress", "dataWebpackPrefix", "l", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "onScriptComplete", "prev", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "p", "createStylesheet", "fullhref", "oldTag", "resolve", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "code", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "then", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}