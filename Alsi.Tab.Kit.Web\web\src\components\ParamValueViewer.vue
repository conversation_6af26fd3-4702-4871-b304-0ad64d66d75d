<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
    class="param-value-dialog"
  >
    <div class="param-value-viewer">
      <!-- 第一行：参数名称、参数类型、参数值来源 -->
      <div class="param-row">
        <div class="param-info-grid">
          <div class="param-info-item" v-if="paramName">
            <div class="param-info-label">参数名称</div>
            <div class="param-info-value param-name">{{ paramName }}</div>
          </div>
          
          <div class="param-info-item" v-if="paramType">
            <div class="param-info-label">数据类型</div>
            <div class="param-info-value param-type">{{ paramType }}</div>
          </div>
          
          <div class="param-info-item">
            <div class="param-info-label">参数值来源</div>
            <div class="param-info-value" :class="paramSource ? 'param-source' : 'no-source'">
              <el-icon v-if="paramSource" class="source-icon">
                <Document />
              </el-icon>
              {{ paramSource || 'CIN 文件' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行：参数说明 -->
      <div class="param-row" v-if="paramDescription">
        <div class="param-info-item">
          <div class="param-info-label">参数说明</div>
          <div class="param-info-value param-description">{{ paramDescription }}</div>
        </div>
      </div>

      <!-- 第三行：参数值 label 和格式化滑竿 -->
      <div class="param-row">
        <div class="param-value-header">
          <div class="param-info-label">参数值</div>
          <div class="format-control" v-if="isComplexValue">
            <span class="format-label">格式化缩进</span>
            <el-slider
              v-model="formatLevel"
              :min="0"
              :max="6"
              :step="1"
              :show-tooltip="true"
              :show-stops="true"
              size="small"
              @input="applyFormatting"
              style="width: 200px;"
            />
          </div>
        </div>
      </div>

      <!-- 第四行：参数输入框 -->
      <div class="param-row">
        <CinCodeEditor
          v-model="displayValue"
          :readonly="readonly"
          :placeholder="readonly ? '' : '请输入参数值...'"
          class="param-value-editor"
          @change="handleCodeChange"
        />
      </div>
    </div>

    <template #footer v-if="!readonly">
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Document, Delete } from '@element-plus/icons-vue';
import CinCodeEditor from './CinCodeEditor.vue';

// Props
interface Props {
  modelValue: boolean;
  value: any;
  readonly?: boolean;
  title?: string;
  paramName?: string;
  paramType?: string;
  paramDescription?: string;
  paramSource?: string;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  title: '参数值'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'update:value': [value: string];
  'confirm': [value: string];
}>();

// 数据
const visible = ref(false);
const displayValue = ref('');
const formatLevel = ref(0);

// 计算属性
const isComplexValue = computed(() => {
  const value = displayValue.value.trim();
  return value.includes('{') && value.includes('}');
});

// 计算属性
const formattedValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '';
  }
  
  if (typeof props.value === 'string') {
    // 直接返回原值，保持CIN格式的多行
    return props.value;
  } else {
    // 如果是对象，转换为字符串
    return String(props.value);
  }
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    displayValue.value = formattedValue.value;
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(() => props.value, () => {
  if (visible.value) {
    displayValue.value = formattedValue.value;
  }
});

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleInput = (value: string) => {
  if (!props.readonly) {
    emit('update:value', value);
  }
};

const handleCodeChange = (value: string) => {
  if (!props.readonly) {
    emit('update:value', value);
  }
};

const handleConfirm = () => {
  if (!props.readonly) {
    emit('confirm', displayValue.value);
  }
  handleClose();
};

// 智能格式化 CIN 格式参数值
const applyFormatting = () => {
  if (!displayValue.value.trim() || formatLevel.value === 0) {
    // 格式化等级为 0 时，压缩为一行
    const compressed = displayValue.value
      .replace(/\s+/g, ' ')
      .replace(/\s*{\s*/g, '{')
      .replace(/\s*}\s*/g, '}')
      .replace(/\s*,\s*/g, ',')
      .trim();
    displayValue.value = compressed;
    return;
  }

  const content = displayValue.value.trim();
  if (!content.includes('{') || !content.includes('}')) {
    return; // 不是复杂格式，不处理
  }

  // 解析并格式化 CIN 格式的结构体
  let formatted = content;
  let currentLevel = 0;
  let result = '';
  let i = 0;

  while (i < formatted.length) {
    const char = formatted[i];
    
    if (char === '{') {
      result += '{';
      currentLevel++;
      
      // 如果当前层级小于等于格式化等级，换行并缩进
      if (currentLevel <= formatLevel.value) {
        result += '\n' + '  '.repeat(currentLevel);
      }
    } else if (char === '}') {
      currentLevel--;
      
      // 如果当前层级小于格式化等级，换行并缩进
      if (currentLevel < formatLevel.value) {
        result += '\n' + '  '.repeat(currentLevel);
      }
      result += '}';
    } else if (char === ',') {
      result += ',';
      
      // 如果当前层级小于等于格式化等级，换行并缩进
      if (currentLevel <= formatLevel.value) {
        result += '\n' + '  '.repeat(currentLevel);
      } else {
        result += ' '; // 同一行用空格分隔
      }
    } else if (char === ' ' || char === '\n' || char === '\r' || char === '\t') {
      // 跳过原有的空白字符，格式化时重新添加
      while (i + 1 < formatted.length && /\s/.test(formatted[i + 1])) {
        i++;
      }
    } else {
      result += char;
    }
    
    i++;
  }

  // 清理多余的空行和空格
  displayValue.value = result
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+$/gm, '')
    .trim();
};

// 监听 displayValue 变化，自动检测格式化等级
watch(displayValue, () => {
  if (isComplexValue.value) {
    // 根据当前内容自动检测格式化等级
    const content = displayValue.value;
    const lines = content.split('\n');
    
    if (lines.length === 1) {
      formatLevel.value = 0;
    } else {
      // 计算最大缩进层级
      let maxLevel = 0;
      lines.forEach(line => {
        const match = line.match(/^(\s*)/);
        if (match) {
          const indentLevel = Math.floor(match[1].length / 2);
          maxLevel = Math.max(maxLevel, indentLevel);
        }
      });
      formatLevel.value = Math.min(maxLevel, 6);
    }
  }
});
</script>

<style scoped>
.param-value-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.param-value-viewer {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.param-row {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.format-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.format-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-right: 10px;
  white-space: nowrap;
}

.param-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.param-info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.param-info-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-word;
  line-height: 1.5;
}

.param-name {
  color: var(--el-text-color-primary);
}

.param-type {
  color: var(--el-text-color-primary);
}

.param-description {
  white-space: pre-wrap;
  line-height: 1.6;
}

.param-source {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-color-primary);
}

.source-icon {
  font-size: 14px;
}

.no-source {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.param-value-editor {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.param-value-editor :deep(.cm-editor) {
  height: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.param-value-editor :deep(.cm-focused) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .param-info-grid {
    grid-template-columns: 1fr;
  }
  
  .param-value-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .format-control {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
