using System;
using Alsi.Tab.Kit.Core.Models;

namespace Alsi.Tab.Kit.Web.Dto
{
    public class AppEntryDto
    {
        public Guid Id { get; set; }

        public AppType AppType { get; set; }

        public string ExePath { get; set; }

        public string AppUrl { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string IconPath { get; set; }

        public string[] Tags { get; set; } = Array.Empty<string>();

        public bool ShowInTopMenu { get; set; }

        public bool ShowInBottomMenu { get; set; }

        public int DisplayOrder { get; set; }

        public bool ShowInHomeCard { get; set; }

        public string Icon { get; set; }

        public bool ExeExists { get; set; }

        public bool IconExists { get; set; }

        public string FullExePath { get; set; }

        public string WorkingDirectory { get; set; }
    }

    public class LaunchAppRequest
    {
        public Guid AppId { get; set; }
    }
}
