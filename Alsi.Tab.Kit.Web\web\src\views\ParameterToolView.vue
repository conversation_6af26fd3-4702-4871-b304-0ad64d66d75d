<template>
  <div class="parameter-tool">
    <!-- 参数编辑主区域 -->
    <div class="main-content">
      <!-- CIN 参数列表卡片 -->
      <div class="cin-parameters-section">
        <div>
          <div class="header-title">
            <h4>CIN 参数列表</h4>
          </div>
          <div class="header-actions">
          </div>
        </div>

        <div class="cin-operations-content">
          <div class="operation-row">
            <el-button type="primary" size="small" @click="selectLocalFile">选择本地 CIN 文件</el-button>

            <el-cascader v-model="selectedTemplatePath" size="small" :options="cascaderOptions"
              v-if="categories.length > 0" placeholder="选择 CIN 模板" style="width: 260px; margin-left: 16px;"
              @change="handleTemplateChange" clearable />

            <!-- 显示当前选择的 CIN 文件名 -->
            <div v-if="currentCinFileName" class="current-cin-file">
              <span class="file-name">{{ currentCinFileName }}</span>
              <el-button type="text" size="small" @click="openCinFileFolder" class="folder-button" title="在文件夹中显示">
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </div>

            <el-button type="primary" size="small" @click="exportCinFile" style="margin-left: auto;"
              :loading="processing">导出 CIN</el-button>
          </div>
        </div>

        <el-divider style="margin: 8px 0" />

        <!-- 搜索和过滤 -->
        <div class="search-bar" v-if="variables.length > 0">
          <el-input v-model="searchKeyword" size="small" placeholder="搜索参数名称..." clearable style="width: 260px;" />
          <div class="filter-info">
            <span>共 {{ filteredVariables.length }} / {{ variables.length }} 个参数</span>
          </div>
        </div>

        <!-- 参数表格 -->
        <div class="parameters-table">
          <el-table :data="filteredVariables" border style="width: 100%; height: 100%;" size="small">
            <el-table-column prop="name" label="参数名" width="200" show-overflow-tooltip />
            <el-table-column prop="type" label="数据类型 (CAPL)" width="200" show-overflow-tooltip />
            <el-table-column label="参数值" min-width="200">
              <template #default="scope">
                <div v-if="isComplexParameter(scope.row)" class="complex-param-cell">
                  <el-tooltip 
                    :content="formatParameterValueForTooltip(parameterValues[scope.row.name])"
                    placement="top" 
                    :show-after="500" 
                    :hide-after="200" 
                    raw-content
                  >
                    <el-input 
                      :model-value="formatParameterValueForDisplay(parameterValues[scope.row.name])" 
                      placeholder="复杂参数值..." 
                      size="small"
                      readonly
                      :class="{ 'modified-param': isParameterModified(scope.row.name), 'readonly-param': true }" 
                    />
                  </el-tooltip>
                </div>
                <el-input 
                  v-else
                  v-model="parameterValues[scope.row.name]" 
                  placeholder="输入参数值" 
                  size="small"
                  @change="onParameterChange(scope.row.name)" 
                  :class="{ 'modified-param': isParameterModified(scope.row.name) }" 
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="text" size="small" @click="editComplexParameter(scope.row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="参数说明" width="250">
              <template #default="scope">
                <el-tooltip v-if="scope.row.description" :content="formatDescriptionForTooltip(scope.row.description)"
                  placement="top" :show-after="500" :hide-after="200" raw-content>
                  <div class="parameter-description-cell">
                    {{ formatDescriptionForDisplay(scope.row.description) }}
                  </div>
                </el-tooltip>
                <span v-else class="no-description">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 数据文件管理 -->
      <DataFileManager @parse-result="handleParseResult" />
    </div>

    <!-- 参数导入结果弹窗 -->
    <ParamParseDialog v-model="parseDialogVisible" :source-file="currentParseFile" :parsed-params="currentParsedParams"
      :cin-params="variables" @apply-params="applyParsedParams" />

    <!-- 复杂参数编辑弹窗 -->
    <ParamValueViewer v-model="complexParamDialogVisible" :value="currentComplexParamValue" :readonly="false"
      title="编辑参数" :param-name="currentComplexParamName" :param-type="currentComplexParamType"
      :param-description="currentComplexParamDescription" :param-source="currentComplexParamSource"
      @confirm="handleComplexParamConfirm" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from "vue";
import {
  appApi,
  CinTemplate,
  CaplVariable,
  CinParameterParseRequest,
  CinParameterRequest,
  SourceFile,
  SourceFileType,
  SourceFileStatus,
  ParsedParam
} from "@/api/appApi";
import { ElMessage } from "element-plus";
import ParamParseDialog from "@/components/ParamParseDialog.vue";
import DataFileManager from "@/components/DataFileManager.vue";
import ParamValueViewer from "@/components/ParamValueViewer.vue";

export default defineComponent({
  name: "ParameterToolView",
  components: {
    ParamParseDialog,
    DataFileManager,
    ParamValueViewer
  },
  setup() {
    // 数据
    const templates = ref<CinTemplate[]>([]);
    const sourceType = ref<string>("template");
    const selectedCategory = ref<string>("");
    const selectedTemplateId = ref<string>("");
    const selectedTemplatePath = ref<string[]>([]);
    const filePath = ref<string>("");
    const variables = ref<CaplVariable[]>([]);
    const parameterValues = ref<{ [key: string]: string }>({});
    const sourceFilePath = ref<string>("");
    const searchKeyword = ref<string>("");

    // 状态
    const parsing = ref(false);
    const processing = ref(false);

    // 源文件管理相关
    const parseDialogVisible = ref(false);
    const currentParseFile = ref<SourceFile | null>(null);
    const currentParsedParams = ref<ParsedParam[]>([]);
    const parameterSources = ref<{ [key: string]: string }>({});

    // 新增状态
    const modifiedParams = ref<Set<string>>(new Set());
    const originalValues = ref<{ [key: string]: string }>({});

    // 复杂参数编辑相关
    const complexParamDialogVisible = ref(false);
    const currentComplexParamValue = ref<string>('');
    const currentComplexParamName = ref<string>('');
    const currentComplexParamType = ref<string>('');
    const currentComplexParamDescription = ref<string>('');
    const currentComplexParamSource = ref<string>('');

    // 计算属性
    const categories = computed(() => {
      const categorySet = new Set(templates.value.map(t => t.category));
      return Array.from(categorySet).filter(c => c);
    });

    const cascaderOptions = computed(() => {
      return categories.value.map(category => ({
        value: category,
        label: category,
        children: templates.value
          .filter(t => t.category === category)
          .map(template => ({
            value: template.id,
            label: template.name
          }))
      }));
    });

    const filteredTemplates = computed(() => {
      if (!selectedCategory.value) return [];
      return templates.value.filter(t => t.category === selectedCategory.value);
    });

    const filteredVariables = computed(() => {
      if (!searchKeyword.value) {
        return variables.value;
      }
      const keyword = searchKeyword.value.toLowerCase();
      return variables.value.filter(v =>
        v.name.toLowerCase().includes(keyword)
      );
    });

    const modifiedParamsCount = computed(() => modifiedParams.value.size);

    const hasUnsavedChanges = computed(() => modifiedParams.value.size > 0);

    // 计算当前CIN文件名
    const currentCinFileName = computed(() => {
      if (sourceType.value === "file" && filePath.value) {
        return getFileName(filePath.value);
      }
      return "";
    });

    // 判断参数是否真正被修改（与初始值不同）
    const isParameterModified = (paramName: string) => {
      const currentValue = parameterValues.value[paramName] || "";
      const originalValue = originalValues.value[paramName] || "";
      return currentValue !== originalValue;
    };

    // 方法
    const loadTemplates = async () => {
      try {
        const response = await appApi.cinParameter.getTemplates();
        templates.value = response.data;

        // 自动选择第一个类别和模板
        if (categories.value.length > 0) {
          selectedCategory.value = categories.value[0];
          onCategoryChange();
        }
      } catch (error) {
        console.error('加载模板失败:', error);
      }
    };

    const onSourceTypeChange = () => {
      // 清空之前的数据
      variables.value = [];
      parameterValues.value = {};
      originalValues.value = {};
      sourceFilePath.value = "";
      modifiedParams.value.clear();
    };

    const onCategoryChange = () => {
      if (filteredTemplates.value.length > 0) {
        selectedTemplateId.value = filteredTemplates.value[0].id;
      } else {
        selectedTemplateId.value = "";
      }
    };

    const selectLocalFile = async () => {
      try {
        const response = await appApi.cinParameter.selectFile();
        if (!response.data) {
          return;
        }

        // 设置源类型为文件
        sourceType.value = "file";
        // 清空之前的选择
        selectedTemplateId.value = '';
        selectedCategory.value = '';
        selectedTemplatePath.value = [];

        filePath.value = response.data;
        // 自动解析选择的文件
        await parseSelectedFile();
      } catch (error) {
        console.error('选择文件失败:', error);
      }
    };

    const parseSelectedFile = async () => {
      if (!filePath.value) return;

      parsing.value = true;
      try {
        const request: CinParameterParseRequest = {
          sourceType: "file",
          filePath: filePath.value
        };

        const response = await appApi.cinParameter.parseFile(request);
        const result = response.data;

        variables.value = result.variables;
        sourceFilePath.value = result.sourceFilePath;

        // 初始化参数值
        parameterValues.value = {};
        originalValues.value = {};
        result.variables.forEach(variable => {
          const value = variable.value || "";
          parameterValues.value[variable.name] = value;
          originalValues.value[variable.name] = value;
        });

        modifiedParams.value.clear();
        ElMessage.success(`成功解析 ${result.variables.length} 个参数`);
      } catch (error) {
        console.error('解析文件失败:', error);
        ElMessage.error('解析文件失败');
      } finally {
        parsing.value = false;
      }
    };

    const handleTemplateChange = (value: string[]) => {
      if (value && value.length === 2) {
        // 设置源类型为模板
        sourceType.value = "template";
        selectedCategory.value = value[0];
        selectedTemplateId.value = value[1];
        // 清空文件路径
        filePath.value = "";
        loadSelectedTemplate();
      }
    };

    const getTemplatesByCategory = (category: string) => {
      return templates.value.filter(t => t.category === category);
    };

    const loadSelectedTemplate = async () => {
      if (!selectedTemplateId.value) return;

      parsing.value = true;
      try {
        const request: CinParameterParseRequest = {
          sourceType: "template",
          templateId: selectedTemplateId.value,
          filePath: ""
        };

        const response = await appApi.cinParameter.parseFile(request);
        const result = response.data;

        variables.value = result.variables;
        sourceFilePath.value = result.sourceFilePath;

        // 初始化参数值
        parameterValues.value = {};
        originalValues.value = {};
        result.variables.forEach(variable => {
          const value = variable.value || "";
          parameterValues.value[variable.name] = value;
          originalValues.value[variable.name] = value;
        });

        modifiedParams.value.clear();
        ElMessage.success(`成功加载模板 ${result.variables.length} 个参数`);
      } catch (error) {
        console.error('加载模板失败:', error);
        ElMessage.error('加载模板失败');
      } finally {
        parsing.value = false;
      }
    };

    const onParameterChange = (paramName: string) => {
      const currentValue = parameterValues.value[paramName] || "";
      const originalValue = originalValues.value[paramName] || "";

      if (currentValue !== originalValue) {
        modifiedParams.value.add(paramName);
      } else {
        modifiedParams.value.delete(paramName);
      }
    };

    const exportCinFile = async () => {
      processing.value = true;
      try {
        const request: CinParameterRequest = {
          sourceType: sourceType.value,
          templateId: sourceType.value === "template" ? selectedTemplateId.value : undefined,
          filePath: sourceType.value === "file" ? filePath.value : "",
          parameterValues: parameterValues.value
        };

        const response = await appApi.cinParameter.processFile(request);
        const result = response.data;

        ElMessage.success(`文件导出成功！`);
        modifiedParams.value.clear();

        await appApi.explorer.openExplorer(result.outputFilePath);

      } catch (error) {
        console.error('导出文件失败:', error);
        ElMessage.error('导出文件失败');
      } finally {
        processing.value = false;
      }
    };

    // 源文件管理相关方法
    const handleParseResult = (file: SourceFile, params: ParsedParam[]) => {
      currentParseFile.value = file;
      currentParsedParams.value = params;
      parseDialogVisible.value = true;
    };

    const applyParsedParams = (params: ParsedParam[]) => {
      params.forEach(param => {
        // 查找对应的变量（不区分大小写）
        const variable = variables.value.find(v => v.name.toLowerCase() === param.name.toLowerCase());
        if (variable) {
          const newValue = String(param.value);
          // 应用参数值
          parameterValues.value[variable.name] = newValue;
          // 记录参数来源
          parameterSources.value[variable.name] = param.source;

          // 检查新值是否与原始值不同，如果不同则标记为修改
          const originalValue = originalValues.value[variable.name] || "";
          if (newValue !== originalValue) {
            modifiedParams.value.add(variable.name);
          } else {
            modifiedParams.value.delete(variable.name);
          }
        }
      });
    };

    const getParameterSource = (paramName: string) => {
      return parameterSources.value[paramName];
    };

    // 判断参数是否为复杂类型
    const isComplexParameter = (variable: CaplVariable) => {
      const value = parameterValues.value[variable.name];
      if (!value) return false;

      // 检查是否是结构体类型（包含大括号）
      const trimmedValue = value.trim();
      if (trimmedValue.startsWith('{') && trimmedValue.includes(',')) {
        return true;
      }

      // 检查是否是JSON格式
      if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {
        try {
          JSON.parse(trimmedValue);
          return true;
        } catch {
          // 不是有效的JSON，但可能是CIN格式的结构体
          return trimmedValue.includes(',') || trimmedValue.includes('{');
        }
      }

      // 检查变量类型是否为复杂类型
      const type = variable.type.toLowerCase();
      return type.includes('struct') || type.includes('array') || type.includes('[]');
    };

    // 编辑复杂参数
    const editComplexParameter = (variable: CaplVariable) => {
      currentComplexParamName.value = variable.name;
      currentComplexParamType.value = variable.type;
      currentComplexParamDescription.value = variable.description || '';
      currentComplexParamSource.value = getParameterSource(variable.name) || '';
      currentComplexParamValue.value = parameterValues.value[variable.name] || '';
      complexParamDialogVisible.value = true;
    };

    // 处理复杂参数编辑确认
    const handleComplexParamConfirm = (value: string) => {
      if (currentComplexParamName.value) {
        parameterValues.value[currentComplexParamName.value] = value;
        onParameterChange(currentComplexParamName.value);
      }
    };

    const getFileName = (path: string) => {
      if (!path) return '';
      return path.split(/[\\/]/).pop() || '';
    };

    const getFileTypeTagType = (fileType: SourceFileType) => {
      switch (fileType) {
        case SourceFileType.Arxml: return 'primary';
        case SourceFileType.Sddb: return 'success';
        case SourceFileType.Ldf: return 'warning';
        default: return '';
      }
    };

    const getAppliedParamsCount = (file: SourceFile) => {
      // 计算该文件已应用的参数数量
      let count = 0;
      Object.keys(parameterSources.value).forEach(paramName => {
        if (parameterSources.value[paramName] === file.fileName) {
          count++;
        }
      });
      return count;
    };

    // 格式化参数说明用于显示（将换行符替换为空格）
    const formatDescriptionForDisplay = (description: string) => {
      if (!description) return '';
      return description.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
    };

    // 格式化参数说明用于 tooltip 显示（保持换行）
    const formatDescriptionForTooltip = (description: string) => {
      if (!description) return '';
      return description.replace(/\n/g, '<br />').trim();
    };

    // 格式化参数值用于显示（将多行压缩为一行）
    const formatParameterValueForDisplay = (value: string) => {
      if (!value) return '';
      // 将多行合并为单行，连续空白变为单个空格
      return value.replace(/\s+/g, ' ').trim();
    };

    // 格式化参数值用于 tooltip 显示（保持多行格式）
    const formatParameterValueForTooltip = (value: string) => {
      if (!value) return '';
      // 保持原始多行格式，但转义HTML用于tooltip显示
      return value.replace(/\n/g, '<br />').trim();
    };

    // 打开CIN文件所在文件夹
    const openCinFileFolder = async () => {
      if (filePath.value) {
        try {
          await appApi.explorer.openExplorer(filePath.value);
        } catch (error) {
          console.error('打开文件夹失败:', error);
          ElMessage.error('打开文件夹失败');
        }
      }
    };

    onMounted(async () => {
      await loadTemplates();
    });

    return {
      templates,
      sourceType,
      selectedCategory,
      selectedTemplateId,
      selectedTemplatePath,
      filePath,
      variables,
      parameterValues,
      originalValues,
      sourceFilePath,
      searchKeyword,
      parsing,
      processing,
      categories,
      cascaderOptions,
      filteredTemplates,
      filteredVariables,
      modifiedParams,
      modifiedParamsCount,
      hasUnsavedChanges,
      currentCinFileName,
      isParameterModified,
      onSourceTypeChange,
      onCategoryChange,
      selectLocalFile,
      handleTemplateChange,
      getTemplatesByCategory,
      loadSelectedTemplate,
      onParameterChange,
      exportCinFile,
      openCinFileFolder,
      getFileName,
      getFileTypeTagType,
      getAppliedParamsCount,
      formatDescriptionForDisplay,
      formatDescriptionForTooltip,
      formatParameterValueForDisplay,
      formatParameterValueForTooltip,
      // 源文件管理相关
      parseDialogVisible,
      currentParseFile,
      currentParsedParams,
      handleParseResult,
      applyParsedParams,
      getParameterSource,
      // 复杂参数编辑相关
      isComplexParameter,
      editComplexParameter,
      handleComplexParamConfirm,
      complexParamDialogVisible,
      currentComplexParamValue,
      currentComplexParamName,
      currentComplexParamType,
      currentComplexParamDescription,
      currentComplexParamSource,
      SourceFileStatus
    };
  },
});
</script>

<style scoped>
.parameter-tool {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color-page);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-base);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.current-file-info {
  padding: 8px 20px;
  background: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-border-color-lighter);
  flex-shrink: 0;
}

.file-info-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: var(--el-color-primary);
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 20px;
  min-height: 0;
  /* 允许flex子元素收缩 */
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.import-info {
  color: var(--el-color-primary);
}



.current-value {
  color: var(--el-text-color-regular);
  font-style: italic;
}

.parameter-source {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-color-primary);
}

.parameter-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.parameter-description-cell {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.no-description {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.no-source {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* CIN 参数列表卡片样式 */
.cin-parameters-section {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 允许flex子元素收缩 */
}

.header-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.current-cin-file {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-cin-file .file-name {
  font-size: 13px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.folder-button {
  color: var(--el-color-primary) !important;
  padding: 2px !important;
  min-height: auto !important;
}

.folder-button:hover {
  color: var(--el-color-primary-dark-2) !important;
}

.cin-operations-content {
  margin-top: 12px;
  flex-shrink: 0;
  /* 不允许收缩 */
}

.operation-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.search-bar {
  margin-bottom: 12px;
  flex-shrink: 0;
  /* 不允许收缩 */
}

.parameters-table {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  /* 允许flex子元素收缩 */
}

/* 已修改参数的输入框样式 */
.modified-param {
  --el-input-bg-color: #f0fdf4 !important;
  --el-input-border-color: #86efac !important;
}

.modified-param:deep(.el-input__wrapper) {
  background-color: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
  box-shadow: 0 0 0 1px var(--el-input-border-color) inset;
}

.modified-param:deep(.el-input__wrapper:hover) {
  border-color: #4ade80;
}

.modified-param:deep(.el-input__wrapper.is-focus) {
  border-color: #22c55e;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* 复杂参数单元格样式 */
.complex-param-cell {
  width: 100%;
}

.readonly-param :deep(.el-input__wrapper) {
  background-color: var(--el-fill-color-light) !important;
  cursor: pointer !important;
}

.readonly-param :deep(.el-input__inner) {
  cursor: pointer !important;
  color: var(--el-text-color-regular) !important;
}

.readonly-param:hover :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7) inset !important;
}



.imported-files-section {
  margin-top: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
}

.imported-files-section h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.imported-files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.imported-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

.imported-file-item .file-name {
  font-weight: 500;
}

.param-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.file-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.template-selection {
  display: flex;
}

.category-list {
  width: 200px;
  border-right: 1px solid var(--el-border-color-lighter);
  overflow-y: auto;
}

.category-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.category-item:hover {
  background: var(--el-fill-color-light);
}

.category-item.active {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
}

.template-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.template-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  cursor: pointer;
}

.template-item:hover {
  border-color: var(--el-color-primary);
}

.template-item.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.template-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .search-bar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .template-selection {
    flex-direction: column;
    height: auto;
  }

  .category-list {
    width: auto;
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-lighter);
    max-height: 150px;
  }
}
</style>
