using System;

namespace Alsi.Tab.Kit.Core.Models
{
    /// <summary>
    /// 应用程序类型（内部使用，不在配置中）
    /// </summary>
    public enum AppType
    {
        /// <summary>
        /// 外部应用程序
        /// </summary>
        External = 0,
        /// <summary>
        /// 内部应用程序
        /// </summary>
        Internal = 1
    }

    /// <summary>
    /// 应用程序模型（支持外部和内部应用）
    /// </summary>
    public class AppEntry
    {
        /// <summary>
        /// 应用程序唯一标识（运行时计算）
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 应用程序类型（运行时自动识别，不在配置中）
        /// </summary>
        public AppType AppType { get; set; } = AppType.External;

        /// <summary>
        /// 可执行文件相对路径（相对于索引文件）- 仅外部应用使用
        /// </summary>
        public string ExePath { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序内部路由地址 - 仅内部应用使用
        /// </summary>
        public string AppUrl { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 图标名称或文件名（根据是否有后缀名区分：有后缀名=文件图标，无后缀名=前端图标）
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public string[] Tags { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 是否在顶部菜单中显示
        /// </summary>
        public bool ShowInTopMenu { get; set; } = false;

        /// <summary>
        /// 是否在底部菜单中显示
        /// </summary>
        public bool ShowInBottomMenu { get; set; } = false;

        /// <summary>
        /// 显示顺序（数字越小越靠前）
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// 是否在主页中显示卡片
        /// </summary>
        public bool ShowInHomeCard { get; set; } = false;

        /// <summary>
        /// 可执行文件完整路径（运行时计算）
        /// </summary>
        public string FullExePath { get; set; } = string.Empty;

        /// <summary>
        /// 图标文件完整路径（运行时计算）
        /// </summary>
        public string FullIconPath { get; set; } = string.Empty;

        /// <summary>
        /// 可执行文件是否存在
        /// </summary>
        public bool ExeExists { get; set; }

        /// <summary>
        /// 图标文件是否存在
        /// </summary>
        public bool IconExists { get; set; }

        /// <summary>
        /// 工作目录（exe文件所在目录）
        /// </summary>
        public string WorkingDirectory { get; set; } = string.Empty;
    }

    /// <summary>
    /// 应用程序索引文件
    /// </summary>
    public class AppEntryIndex
    {
        public AppEntry[] Apps { get; set; } = Array.Empty<AppEntry>();
    }
}
