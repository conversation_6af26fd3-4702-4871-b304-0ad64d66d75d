using Alsi.Common.Parsers.Cin;
using Alsi.Common.Parsers.Cin.Models;

namespace Alsi.Tab.Kit.UnitTests;

public class CaplUtils_ParseTests
{
    public CaplUtils_ParseTests()
    {
        CinParser.Initialize();
    }

    [Fact]
    public void ParseToStructure_ShouldReturnSuccessForValidFile()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public void ParseToStructure_ShouldIdentifyIncludesBlock()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var includesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Include);
        includesBlock.ShouldNotBeNull();
        includesBlock.Content.ShouldContain("includes");
        includesBlock.Content.ShouldContain("#include");
    }

    [Fact]
    public void ParseToStructure_ShouldIdentifyVariablesBlock()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.Content.ShouldContain("variables");
        variablesBlock.Variables.ShouldNotBeNull();
        variablesBlock.Variables.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public void ParseToStructure_ShouldExtractVariableDeclarations()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        // 验证变量声明

        var variableNames = new[]
{
            "ECU_NAME_STR",
            "MSGID_CTRL_BUSLOAD",
            "REQ_ReadDID_ResIsMF",
            "IS_ECU_GW",
            "BUS_CAN_CANFD",
            "APP_IDs",
            "supportSIDandSubIDs",
            "PIN_CODES",
            "REQUIRED_NUM_OF_SWDL_ITERATIONS",
            "IS_RID0205_NEED_SA",
            "NM_ID"
        };
        variablesBlock.Variables.Count.ShouldBe(variableNames.Length);
        var multipleLinesA = string.Join(Environment.NewLine, variablesBlock.ParsedVariables.OrderBy(x => x.Name).Select(x => x.Name));
        var multipleLinesB = string.Join(Environment.NewLine, variableNames.OrderBy(x => x));
        multipleLinesA.ShouldBe(multipleLinesB);
    }

    [Fact]
    public void ParseToStructure_ShouldMaintainLineNumbers()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();

        foreach (var block in result.CodeBlocks)
        {
            block.StartLine.ShouldBeGreaterThan(0);
            block.EndLine.ShouldBeGreaterThanOrEqualTo(block.StartLine);
        }
    }

    [Fact]
    public void ParseToStructure_ShouldHandleCommentsCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo1.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ProcessedContent.ShouldNotBeNull();

        // 处理后的内容不应包含注释标记，但应保持行数
        var originalLines = System.IO.File.ReadAllLines(caplPath);
        var processedLines = result.ProcessedContent.Split('\n');
        processedLines.Length.ShouldBe(originalLines.Length);
    }

    [Fact]
    public void ParseToStructure_ShouldHandleComplexVariableDeclarations()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo1.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        // 验证复杂的变量声明（如数组、结构体）
        variablesBlock.Variables.ShouldContain(v => v.Contains("APP_IDs"));
        variablesBlock.Variables.ShouldContain(v => v.Contains("supportSIDandSubIDs"));
        variablesBlock.Variables.ShouldContain(v => v.Contains("PIN_CODES"));
    }

    [Fact]
    public void ParseToStructure_ShouldReturnErrorForNonExistentFile()
    {
        // Arrange
        var nonExistentPath = "./NonExistent.cin";

        // Act
        Should.Throw<Exception>(() =>
        {
            CinParser.ParseFile(nonExistentPath);
        });
    }

    [Fact]
    public void ParseToStructure_ShouldHandleEmptyVariablesBlock()
    {
        var testContent = @"/*@!Encoding:936*/
includes{
    #include ""test.cin""
}

variables{
}";

        // Act
        var result = CinParser.ParseContent(testContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.Variables.ShouldBeEmpty();
    }

    [Fact]
    public void ParseToStructure_ShouldParseVariableDetails()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.ParsedVariables.ShouldNotBeNull();
        variablesBlock.ParsedVariables.Count.ShouldBeGreaterThan(0);

        // 验证特定变量的解析
        var ecuNameVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "ECU_NAME_STR");
        ecuNameVar.ShouldNotBeNull();
        ecuNameVar.Type.ShouldBe("char");
        ecuNameVar.IsArray.ShouldBeTrue();
        ecuNameVar.ArraySize.ShouldBe("40");
        ecuNameVar.IsConst.ShouldBeFalse();
        ecuNameVar.Value.ShouldBe("\"BasicDiagnosticsEcu\"");
    }

    [Fact]
    public void ParseToStructure_ShouldParseConstVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var constVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "MSGID_CTRL_BUSLOAD");
        constVar.ShouldNotBeNull();
        constVar.Type.ShouldBe("dword");
        constVar.IsConst.ShouldBeTrue();
        constVar.IsArray.ShouldBeFalse();
        constVar.Value.ShouldBe("0x1");
    }

    [Fact]
    public void ParseToStructure_ShouldParseArrayVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var arrayVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "APP_IDs");
        arrayVar.ShouldNotBeNull();
        arrayVar.Type.ShouldBe("dword");
        arrayVar.IsArray.ShouldBeTrue();
        arrayVar.ArraySize.ShouldBe("7");
        arrayVar.Value.ShouldContain("{0x3F,0x57,0x158,0x173,0x22E,0x2D1,0x510}");
    }

    [Fact]
    public void ParseToStructure_ShouldParseStructArrayVariables()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo2.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var structVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "supportSIDandSubIDs");
        structVar.ShouldNotBeNull();
        structVar.Type.ShouldBe("struct STRU_SID_SUBID");
        structVar.IsArray.ShouldBeTrue();
        structVar.ArraySize.ShouldBe("20");
        structVar.Value.ShouldContain("{7,0x10,3,{1,2,3}}");
    }

    [Fact]
    public void ParseToStructure_ShouldParseParamDemoDiagCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemoDiag.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.CodeBlocks.Count.ShouldBe(1);

        var variablesBlock = result.CodeBlocks[0];
        variablesBlock.ContentType.ShouldBe(CinContentType.Variables);

        var actualNames = variablesBlock.ParsedVariables.Select(x => x.Name).OrderBy(x => x).ToArray();
        var expectedNames = new[]
        {
            "B_IDLE",
            "AMSK",
            "DIAG_REQ_ID",
            "DIAG_RES_ID",
            "DIAG_FUNC_REQ_ID",
            "N_As",
            "N_Ar",
            "N_Bs",
            "N_Cr",
            "P2time",
            "P2time_pro",
            "P2ExtTime",
            "S3time",
            "N_WFTmax",
            "MSGID_CTRL_BUSLOAD",
            "SEC_ACCESS_XOR_BYTES",
            "REQ_ReadDID_Session",
            "RES_ReadDID_Session01",
            "RES_ReadDID_Session02",
            "RES_ReadDID_Session03",
            "REQSTR_ReadDID_Session",
            "RESSTR_ReadDID_Session01",
            "RESSTR_ReadDID_Session02",
            "RESSTR_ReadDID_Session03",
            "REQ_ReadDID_ResIsMF",
            "RES_ReadDID_ResIsMF_FF",
            "BUS_CAN_CANFD",
            "CHANNEL",
            "POWERTYPE",
            "ECU_NAME_STR",
            "SEED_LENGTH",
            "errorMakeConditions",
            "errorMakeConditionsVOL",
            "dutCarUsageMode",
            "ubFlag",
            "carFlag",
            "useFlag",
            "eleFlag",
            "gUseMode",
            "gCarMode",
            "gElPowerLevel",
            "gTestErrAliveFlag",
            "gTestErrCheckFlag",
            "gUbatVoltage",
            "checkDTC1",
            "checkDTC2",
            "supportSIDandSubIDs",
            "supportDIDs0x22",
            "supportDIDs0x2E",
            "supportRIDs",
            "supportIOIDs",
            "snapshotInfoList",
            "dtcExtData",
            "supDTCList",
            "ecuDID_TG52",
            "ecuDID_TG66",
        }.OrderBy(x => x).ToArray();

        // 验证变量数量
        actualNames.Length.ShouldBe(expectedNames.Length,
            $"Expected {expectedNames.Length} variables, but got {actualNames.Length}. " +
            $"Missing: {string.Join(", ", expectedNames.Except(actualNames))}. " +
            $"Extra: {string.Join(", ", actualNames.Except(expectedNames))}");

        // 验证每个变量名
        for (var i = 0; i < expectedNames.Length; i++)
        {
            actualNames[i].ShouldBe(expectedNames[i], $"Variable at index {i} mismatch");
        }
    }

    [Fact]
    public void ParseToStructure_ShouldParseMultiVariableDeclarationsFromParamDemoDiag()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemoDiag.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        // 验证多变量声明：byte ubFlag,carFlag,useFlag,eleFlag;
        var ubFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "ubFlag");
        ubFlagVar.ShouldNotBeNull();
        ubFlagVar.Type.ShouldBe("byte");
        ubFlagVar.IsArray.ShouldBeFalse();
        ubFlagVar.IsConst.ShouldBeFalse();
        ubFlagVar.Value.ShouldBe("");

        var carFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "carFlag");
        carFlagVar.ShouldNotBeNull();
        carFlagVar.Type.ShouldBe("byte");
        carFlagVar.IsArray.ShouldBeFalse();
        carFlagVar.IsConst.ShouldBeFalse();
        carFlagVar.Value.ShouldBe("");

        var useFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "useFlag");
        useFlagVar.ShouldNotBeNull();
        useFlagVar.Type.ShouldBe("byte");
        useFlagVar.IsArray.ShouldBeFalse();
        useFlagVar.IsConst.ShouldBeFalse();
        useFlagVar.Value.ShouldBe("");

        var eleFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "eleFlag");
        eleFlagVar.ShouldNotBeNull();
        eleFlagVar.Type.ShouldBe("byte");
        eleFlagVar.IsArray.ShouldBeFalse();
        eleFlagVar.IsConst.ShouldBeFalse();
        eleFlagVar.Value.ShouldBe("");

        // 验证多变量声明：byte gUseMode=0,gCarMode=0,gElPowerLevel=0;
        var gUseModeVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gUseMode");
        gUseModeVar.ShouldNotBeNull();
        gUseModeVar.Type.ShouldBe("byte");
        gUseModeVar.IsArray.ShouldBeFalse();
        gUseModeVar.IsConst.ShouldBeFalse();
        gUseModeVar.Value.ShouldBe("0");

        var gCarModeVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gCarMode");
        gCarModeVar.ShouldNotBeNull();
        gCarModeVar.Type.ShouldBe("byte");
        gCarModeVar.IsArray.ShouldBeFalse();
        gCarModeVar.IsConst.ShouldBeFalse();
        gCarModeVar.Value.ShouldBe("0");

        var gElPowerLevelVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gElPowerLevel");
        gElPowerLevelVar.ShouldNotBeNull();
        gElPowerLevelVar.Type.ShouldBe("byte");
        gElPowerLevelVar.IsArray.ShouldBeFalse();
        gElPowerLevelVar.IsConst.ShouldBeFalse();
        gElPowerLevelVar.Value.ShouldBe("0");

        // 验证多变量声明：byte gTestErrAliveFlag=0,gTestErrCheckFlag=0;
        var gTestErrAliveFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gTestErrAliveFlag");
        gTestErrAliveFlagVar.ShouldNotBeNull();
        gTestErrAliveFlagVar.Type.ShouldBe("byte");
        gTestErrAliveFlagVar.IsArray.ShouldBeFalse();
        gTestErrAliveFlagVar.IsConst.ShouldBeFalse();
        gTestErrAliveFlagVar.Value.ShouldBe("0");

        var gTestErrCheckFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gTestErrCheckFlag");
        gTestErrCheckFlagVar.ShouldNotBeNull();
        gTestErrCheckFlagVar.Type.ShouldBe("byte");
        gTestErrCheckFlagVar.IsArray.ShouldBeFalse();
        gTestErrCheckFlagVar.IsConst.ShouldBeFalse();
        gTestErrCheckFlagVar.Value.ShouldBe("0");

        // 验证其他常量变量的正确解析
        var diagReqIdVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "DIAG_REQ_ID");
        diagReqIdVar.ShouldNotBeNull();
        diagReqIdVar.Type.ShouldBe("dword");
        diagReqIdVar.IsArray.ShouldBeFalse();
        diagReqIdVar.IsConst.ShouldBeTrue();
        diagReqIdVar.Value.ShouldBe("0x731");

        var gUbatVoltageVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gUbatVoltage");
        gUbatVoltageVar.ShouldNotBeNull();
        gUbatVoltageVar.Type.ShouldBe("byte");
        gUbatVoltageVar.IsArray.ShouldBeFalse();
        gUbatVoltageVar.IsConst.ShouldBeFalse();
        gUbatVoltageVar.Value.ShouldBe("0x78");
    }

    [Fact]
    public void ParseToStructure_ShouldHandleMultiVariableDeclarations()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    // 测试一行多个变量声明
    byte ubFlag,carFlag,useFlag,eleFlag;
    byte gUseMode=0,gCarMode=0,gElPowerLevel=0;
    byte gTestErrAliveFlag=0,gTestErrCheckFlag=0;
    const dword DIAG_REQ_ID = 0x731, DIAG_RES_ID = 0x631;
    int singleVar = 100;
}";

        // Act
        var result = CinParser.ParseContent(testContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();
        variablesBlock.ParsedVariables.ShouldNotBeNull();

        // 验证解析出的变量数量
        var expectedVariableNames = new[]
        {
            "ubFlag", "carFlag", "useFlag", "eleFlag",
            "gUseMode", "gCarMode", "gElPowerLevel",
            "gTestErrAliveFlag", "gTestErrCheckFlag",
            "DIAG_REQ_ID", "DIAG_RES_ID",
            "singleVar"
        };

        var actualVariableNames = variablesBlock.ParsedVariables.Select(v => v.Name).OrderBy(n => n).ToArray();
        var expectedSorted = expectedVariableNames.OrderBy(n => n).ToArray();

        actualVariableNames.Length.ShouldBe(expectedSorted.Length);
        for (int i = 0; i < expectedSorted.Length; i++)
        {
            actualVariableNames[i].ShouldBe(expectedSorted[i]);
        }

        // 验证特定变量的属性
        var ubFlagVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "ubFlag");
        ubFlagVar.ShouldNotBeNull();
        ubFlagVar.Type.ShouldBe("byte");
        ubFlagVar.IsArray.ShouldBeFalse();
        ubFlagVar.IsConst.ShouldBeFalse();
        ubFlagVar.Value.ShouldBe("");

        var gUseModeVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "gUseMode");
        gUseModeVar.ShouldNotBeNull();
        gUseModeVar.Type.ShouldBe("byte");
        gUseModeVar.IsArray.ShouldBeFalse();
        gUseModeVar.IsConst.ShouldBeFalse();
        gUseModeVar.Value.ShouldBe("0");

        var diagReqIdVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "DIAG_REQ_ID");
        diagReqIdVar.ShouldNotBeNull();
        diagReqIdVar.Type.ShouldBe("dword");
        diagReqIdVar.IsArray.ShouldBeFalse();
        diagReqIdVar.IsConst.ShouldBeTrue();
        diagReqIdVar.Value.ShouldBe("0x731");

        var singleVarVar = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "singleVar");
        singleVarVar.ShouldNotBeNull();
        singleVarVar.Type.ShouldBe("int");
        singleVarVar.IsArray.ShouldBeFalse();
        singleVarVar.IsConst.ShouldBeFalse();
        singleVarVar.Value.ShouldBe("100");
    }

    [Fact]
    public void ParseContent_ShouldPreserveMultilineArrayFormat()
    {
        // Arrange
        var caplContent = @"
variables
{
    int arrayParam[3] = {
        1,
        2,
        3
    };
}";

        // Act
        var result = CinParser.ParseContent(caplContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var arrayVariable = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "arrayParam");
        arrayVariable.ShouldNotBeNull();
        arrayVariable.Value.ShouldContain("\n");  // Should preserve newlines
        arrayVariable.Value.ShouldContain("    1"); // Should preserve indentation
    }

    [Fact]
    public void ParseContent_ShouldPreserveMultilineStructFormat()
    {
        // Arrange
        var caplContent = @"
variables
{
    struct TestStruct structParam = {
        10,
        20,
        30
    };
}";

        // Act
        var result = CinParser.ParseContent(caplContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var structVariable = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "structParam");
        structVariable.ShouldNotBeNull();
        structVariable.Value.ShouldContain("\n");  // Should preserve newlines
        structVariable.Value.ShouldContain("        10"); // Should preserve indentation
    }

    [Fact]
    public void ParseContent_ShouldHandleMultiVariableDeclarationWithMultilineValues()
    {
        // Arrange
        var caplContent = @"
variables
{
    int var1[2] = {
        1,
        2
    };
    int var2 = 0;
int var3[3] = {
        3,
        4,
        5
    };
}";

        // Act
        var result = CinParser.ParseContent(caplContent);

        // Assert
        result.Success.ShouldBeTrue();
        var variablesBlock = result.CodeBlocks.FirstOrDefault(b => b.ContentType == CinContentType.Variables);
        variablesBlock.ShouldNotBeNull();

        var var1 = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "var1");
        var1.ShouldNotBeNull();
        var1.Value.ShouldContain("\n");  // Should preserve newlines for var1

        var var2 = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "var2");
        var2.ShouldNotBeNull();
        var2.Value.ShouldBe("0");  // Simple value should remain simple

        var var3 = variablesBlock.ParsedVariables.FirstOrDefault(v => v.Name == "var3");
        var3.ShouldNotBeNull();
        var3.Value.ShouldContain("\n");  // Should preserve newlines for var3
    }
}