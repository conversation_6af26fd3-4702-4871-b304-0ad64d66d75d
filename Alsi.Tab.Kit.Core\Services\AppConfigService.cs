using Alsi.App;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services
{
    public class AppConfigService
    {
        private const string APP_CONFIG_FOLDER = "app_config";
        private const string INDEX_FILE_NAME = "app_config.json";

        private static AppEntry[] cache = null;
        private static object cacheLock = new object();
        private static DateTime? cacheTime = null;

        public AppEntry[] GetApps()
        {
            lock (cacheLock)
            {
                var modificationTime = GetModificationTime();
                if (cache == null || cacheTime == null || cacheTime < modificationTime)
                {
                    cache = LoadApps();
                    cacheTime = modificationTime;
                }

                return cache;
            }
        }

        private DateTime? GetModificationTime()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return null;
            }
            return new FileInfo(indexFilePath).LastWriteTime;
        }

        private AppEntry[] LoadApps()
        {
            var indexFilePath = GetIndexFilePath();
            if (!File.Exists(indexFilePath))
            {
                return Array.Empty<AppEntry>();
            }

            var jsonContent = File.ReadAllText(indexFilePath);
            var appsIndex = JsonUtils.Deserialize<AppEntryIndex>(jsonContent);

            if (appsIndex?.Apps == null)
            {
                return Array.Empty<AppEntry>();
            }

            var appConfigFolder = GetAppConfigFolder();

            foreach (var app in appsIndex.Apps)
            {
                app.Id = Guid.NewGuid();

                // 自动识别应用类型
                if (!string.IsNullOrWhiteSpace(app.AppUrl))
                {
                    app.AppType = AppType.Internal;
                }
                else if (!string.IsNullOrWhiteSpace(app.ExePath))
                {
                    app.AppType = AppType.External;
                }
                else
                {
                    app.AppType = AppType.External; // 默认为外部应用
                }

                ProcessAppPaths(app, appConfigFolder);
            }

            return appsIndex.Apps;
        }

        public void LaunchExternalApp(Guid appId)
        {
            var apps = GetApps();
            var app = apps.FirstOrDefault(x => x.Id == appId);

            if (app == null)
            {
                throw new AppException($"找不到 ID 为 '{appId}' 的应用程序");
            }

            // 内部应用不需要启动外部进程
            if (app.AppType == AppType.Internal)
            {
                AppEnv.Logger.Info($"Internal APP launched: Name={app.Name}; ID={app.Id}; AppUrl={app.AppUrl}");
                return;
            }

            // 外部应用需要检查可执行文件存在
            if (!app.ExeExists)
            {
                throw new AppException($"应用程序 '{app.Name ?? app.Id.ToString()}' 的可执行文件不存在: {app.FullExePath}");
            }

            var startInfo = new ProcessStartInfo
            {
                FileName = app.FullExePath,
                WorkingDirectory = app.WorkingDirectory,
                UseShellExecute = true
            };

            AppEnv.Logger.Info($"Start external APP: Name={app.Name}; ID={app.Id}; FullExePath={app.FullExePath}; WorkingDirectory={app.WorkingDirectory}");
            Process.Start(startInfo);
        }

        private void ProcessAppPaths(AppEntry app, string externalAppsFolder)
        {
            // 处理外部应用的路径
            if (app.AppType == AppType.External)
            {
                if (!string.IsNullOrWhiteSpace(app.ExePath))
                {
                    app.FullExePath = Path.Combine(externalAppsFolder, app.ExePath);
                    app.ExeExists = File.Exists(app.FullExePath);

                    if (app.ExeExists)
                    {
                        app.WorkingDirectory = Path.GetDirectoryName(app.FullExePath);
                    }
                }
                else
                {
                    app.ExeExists = false;
                }
            }
            else
            {
                // 内部应用始终视为"存在"
                app.ExeExists = true;
            }

            if (!string.IsNullOrWhiteSpace(app.Icon))
            {
                // 判断是否为文件图标（有后缀名）
                if (Path.HasExtension(app.Icon))
                {
                    app.FullIconPath = Path.Combine(externalAppsFolder, app.Icon);
                    app.IconExists = File.Exists(app.FullIconPath);
                }
                else
                {
                    // 前端图标（无后缀名），不需要检查文件存在
                    app.IconExists = true;
                }
            }
            else
            {
                app.IconExists = false;
            }

            if (string.IsNullOrWhiteSpace(app.Name))
            {
                if (app.AppType == AppType.External && !string.IsNullOrWhiteSpace(app.ExePath))
                {
                    app.Name = Path.GetFileNameWithoutExtension(app.ExePath);
                }
                else if (app.AppType == AppType.Internal && !string.IsNullOrWhiteSpace(app.AppUrl))
                {
                    app.Name = app.AppUrl.TrimStart('/');
                }
            }

            if (app.Tags == null)
            {
                app.Tags = Array.Empty<string>();
            }

            if (app.AppType == AppType.External)
            {
                var commonTagName = "独立应用";
                if (!app.Tags.Contains(commonTagName))
                {
                    var tagList = app.Tags.ToList();
                    tagList.Insert(0, commonTagName);
                    app.Tags = tagList.ToArray();
                }
            }
        }

        private string GetAppConfigFolder()
        {
            return Path.Combine(Directory.GetParent(GetType().Assembly.Location).FullName, APP_CONFIG_FOLDER);
        }

        private string GetIndexFilePath()
        {
            return Path.Combine(GetAppConfigFolder(), INDEX_FILE_NAME);
        }
    }
}
