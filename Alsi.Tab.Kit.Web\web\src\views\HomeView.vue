<template>
  <div class="home">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>TabKit</h1>
      <p>TabKit 中集成了多种实用工具</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 功能卡片网格 -->
    <div v-else class="tools-grid">
      <!-- 配置的应用程序卡片 -->
      <el-card v-for="app in homeDisplayApps" :key="app.id" class="tool-card external-app-card" :class="{
        'app-unavailable': !app.exeExists
      }" shadow="hover" @click="handleAppClick(app)">
        <!-- 不可用状态标识 -->
        <div v-if="!app.exeExists && app.appType === AppType.External" class="external-app-badge">
          <el-tag size="small" type="danger">不可用</el-tag>
        </div>

        <!-- 应用图标 -->
        <div class="tool-icon">
          <img v-if="app.iconExists && hasFileExtension(app.icon)" :src="getAppIconUrl(app.id)" :alt="app.name"
            class="app-icon" @error="handleIconError($event)" />
          <font-awesome-icon v-else-if="app.icon && !hasFileExtension(app.icon)" :icon="app.icon"
            class="default-app-icon" />
          <font-awesome-icon v-else icon="cube" class="default-app-icon" />
        </div>

        <!-- 应用信息 -->
        <h3>{{ app.name || 'Unknown App' }}</h3>
        <p>{{ app.description || '无描述信息' }}</p>

        <!-- 应用标签 -->
        <div v-if="app.tags && app.tags.length > 0" class="tool-features">
          <el-tag v-for="tag in app.tags" :key="tag" size="small" :type="getTagColor(tag)">
            {{ tag }}
          </el-tag>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { appApi, AppEntry, AppType } from "@/api/appApi";
import { ElMessage, ElMessageBox } from 'element-plus';

export default defineComponent({
  name: "HomeView",
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const router = useRouter();
    const appEntries = ref<AppEntry[]>([]);
    const loading = ref(false);

    const navigateToTool = (path: string) => {
      router.push(path);
    };

    // 计算属性：过滤需要在主页显示的应用
    const homeDisplayApps = computed(() => {
      return appEntries.value
        .filter(app => app.showInHomeCard)
        .sort((a, b) => a.displayOrder - b.displayOrder);
    });

    // 生成标签颜色的哈希函数，映射到 Element Plus 预定义类型
    const getTagColor = (tag: string): string => {
      const colors = ['', 'success', 'warning', 'info'];
      let hash = 0;
      for (let i = 0; i < tag.length; i++) {
        hash = tag.charCodeAt(i) + ((hash << 5) - hash);
      }
      return colors[Math.abs(hash) % colors.length];
    };

    // 获取应用图标 URL
    const getAppIconUrl = (appId: string): string => {
      return appApi.appConfig.getIconUrl(appId);
    };

    // 处理图标加载错误
    const handleIconError = (event: Event) => {
      const target = event.target as HTMLImageElement;
      target.style.display = 'none';
      // 可以在这里添加显示默认图标的逻辑
    };

    // 检查是否有文件扩展名
    const hasFileExtension = (filename: string): boolean => {
      if (!filename) return false;
      const dotIndex = filename.lastIndexOf('.');
      return dotIndex > 0 && dotIndex < filename.length - 1;
    };

    // 加载应用程序列表
    const loadAppEntries = async () => {
      try {
        loading.value = true;
        const response = await appApi.appConfig.getList();
        appEntries.value = response.data;
      } catch (error) {
        console.error('加载应用程序失败:', error);
        // 静默处理错误，不显示错误消息，因为可能是没有配置应用程序
      } finally {
        loading.value = false;
      }
    };

    // 处理应用点击事件
    const handleAppClick = async (app: AppEntry) => {
      if (app.appType === AppType.Internal) {
        // 内部应用：跳转到指定路由
        if (app.appUrl) {
          navigateToTool(app.appUrl);
        }
      } else {
        // 外部应用：启动外部程序
        await launchApp(app);
      }
    };

    // 启动应用程序
    const launchApp = async (app: AppEntry) => {
      if (!app.exeExists) {
        ElMessage.error(`应用 "${app.name}" 的可执行文件不存在`);
        return;
      }

      await ElMessageBox.confirm(`是否启动 "${app.name}" ?`, '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      });

      try {
        await appApi.appConfig.launch({ appId: app.id });
        ElMessage.success(`正在启动 "${app.name}"...`);
      } catch (error: any) {
        console.error('启动应用失败:', error);
        ElMessage.error(error.response?.data?.message || `启动 "${app.name}" 失败`);
      }
    };

    onMounted(() => {
      loadAppEntries();
    });

    return {
      navigateToTool,
      appEntries,
      homeDisplayApps,
      loading,
      getTagColor,
      handleAppClick,
      launchApp,
      getAppIconUrl,
      handleIconError,
      hasFileExtension,
      AppType,
    };
  },
});
</script>

<style scoped>
.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-top: 40px;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--el-text-color-primary);
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.1rem;
  color: var(--el-text-color-regular);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.tool-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tool-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tool-card.coming-soon {
  opacity: 0.7;
  cursor: not-allowed;
}

.tool-card.coming-soon:hover {
  transform: none;
  box-shadow: none;
}

.tool-icon {
  font-size: 2rem;
  color: var(--el-color-primary);
  margin-bottom: 16px;

  .logo-icon {
    width: 2rem;
    height: 2rem;
  }
}

.tool-card h3 {
  font-size: 1.3rem;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.tool-card p {
  color: var(--el-text-color-regular);
  line-height: 1.5;
  margin-bottom: 16px;
  flex-grow: 1;
  font-size: 0.9rem;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* External Apps 相关样式 */

.external-app-card {
  position: relative;
}

.external-app-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.external-app-card.app-unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.external-app-card.app-unavailable:hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.app-icon {
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}

.default-app-icon {
  font-size: 2rem;
  color: var(--el-color-primary);
}

.app-status {
  margin-bottom: 10px;
}

/* 加载状态样式 */
.loading-container {
  padding: 20px;
}

@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
