<template>
  <div class="data-files-section">
    <div class="section-header">
      <h4>数据文件 (ARXML/SDDB/LDF)</h4>
      <div class="header-actions">
        <el-button size="small" type="primary" @click="selectDataFiles">添加文件</el-button>
        <el-button size="small" type="danger" @click="clearAllDataFiles" v-if="dataFiles.length > 0">清空</el-button>
      </div>
    </div>

    <div class="data-files-table">
      <el-table :data="dataFiles" border style="width: 100%" size="small">
        <el-table-column label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getFileTypeTagType(scope.row.fileType)" size="small">{{ scope.row.fileType.toUpperCase()
              }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="文件名" min-width="200">
          <template #default="scope">
            <div class="file-name" :title="scope.row.path">
              <span class="name">{{ scope.row.fileName }}</span>
              <el-button type="text" size="small" @click="openDataFileFolder(scope.row.path)" class="folder-button"
                title="在文件夹中显示">
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">{{ getStatusText(scope.row.status)
              }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                v-if="scope.row.status === SourceFileStatus.Pending || scope.row.status === SourceFileStatus.Error"
                type="primary" size="small" @click="parseDataFile(scope.row.id)">
                解析参数
              </el-button>

              <el-button v-if="scope.row.status === SourceFileStatus.Parsed" type="success" size="small"
                @click="viewDataFileDetails(scope.row)">
                查看结果
              </el-button>

              <el-button type="danger" size="small" @click="removeDataFile(scope.row.id)">
                移除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import {
  appApi,
  SourceFile,
  SourceFileType,
  SourceFileStatus
} from "@/api/appApi";
import { ElMessage, ElMessageBox } from "element-plus";

export default defineComponent({
  name: "DataFileManager",
  emits: ['parse-result'],
  setup(props, { emit }) {
    // 数据
    const dataFiles = ref<SourceFile[]>([]);

    // 方法
    const loadDataFiles = async () => {
      try {
        const response = await appApi.cinParameter.getSourceFiles();
        dataFiles.value = response.data;
      } catch (error) {
        console.error('加载数据文件失败:', error);
        ElMessage.error('加载数据文件失败');
      }
    };

    const selectDataFiles = async () => {
      try {
        const response = await appApi.cinParameter.selectSourceFiles();
        const filePaths = response.data;

        if (filePaths && filePaths.length > 0) {
          await addDataFiles(filePaths);
          ElMessage.success(`成功添加 ${filePaths.length} 个参数数据文件`);
        }
      } catch (error) {
        console.error('选择文件失败:', error);
        ElMessage.error('选择文件失败');
      }
    };

    const addDataFiles = async (filePaths: string[]) => {
      try {
        const response = await appApi.cinParameter.addSourceFiles({ filePaths });
        const newFiles = response.data;

        // 更新导入文件列表
        newFiles.forEach(file => {
          const existingIndex = dataFiles.value.findIndex(f => f.id === file.id);
          if (existingIndex >= 0) {
            dataFiles.value[existingIndex] = file;
          } else {
            dataFiles.value.push(file);
          }
        });

        return newFiles;
      } catch (error) {
        console.error('添加文件失败:', error);
        throw error;
      }
    };

    const parseDataFile = async (fileId: string) => {
      try {
        // 先更新本地状态
        const file = dataFiles.value.find(f => f.id === fileId);
        if (file) {
          file.status = SourceFileStatus.Parsing;
        }

        const response = await appApi.cinParameter.parseSourceFile({
          fileId
        });

        const updatedFile = response.data;

        // 更新文件列表中的对应项
        const index = dataFiles.value.findIndex(f => f.id === fileId);
        if (index >= 0) {
          dataFiles.value[index] = updatedFile;
        }

        ElMessage.success(`参数解析完成，共解析出 ${updatedFile.parsedParams?.length || 0} 个参数`);

        // 发送解析结果事件给父组件
        if (updatedFile.parsedParams && updatedFile.parsedParams.length > 0) {
          emit('parse-result', updatedFile, updatedFile.parsedParams);
        }
      } catch (error) {
        console.error('解析文件失败:', error);
        ElMessage.error('参数解析失败');

        // 恢复状态
        const file = dataFiles.value.find(f => f.id === fileId);
        if (file) {
          file.status = SourceFileStatus.Error;
        }
      }
    };

    const viewDataFileDetails = (file: SourceFile) => {
      if (file.parsedParams && file.parsedParams.length > 0) {
        emit('parse-result', file, file.parsedParams);
      } else {
        ElMessage.warning('该文件暂无解析结果');
      }
    };

    const openDataFileFolder = async (filePath: string) => {
      try {
        await appApi.explorer.openExplorer(filePath);
      } catch (error) {
        console.error('打开文件夹失败:', error);
        ElMessage.error('打开文件夹失败');
      }
    };

    const removeDataFile = async (fileId: string) => {
      try {
        // 调用服务端接口移除文件
        await appApi.cinParameter.removeSourceFile({ fileId });

        // 从本地列表中移除文件
        const fileIndex = dataFiles.value.findIndex(f => f.id === fileId);
        if (fileIndex >= 0) {
          dataFiles.value.splice(fileIndex, 1);
          ElMessage.success('已移除数据文件');
        }
      } catch (error) {
        console.error('移除文件失败:', error);
        ElMessage.error('移除文件失败');
      }
    };

    const clearAllDataFiles = async () => {
      try {
        await ElMessageBox.confirm('确定要清空所有参数数据文件吗？', '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 调用服务端接口清空所有文件
        await appApi.cinParameter.clearSourceFiles();

        // 清空本地文件列表
        dataFiles.value = [];
        ElMessage.success('已清空所有参数数据文件');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('清空文件失败:', error);
          ElMessage.error('清空文件失败');
        }
      }
    };

    // 辅助方法
    const getFileTypeTagType = (fileType: SourceFileType) => {
      switch (fileType) {
        case SourceFileType.Arxml: return 'primary';
        case SourceFileType.Sddb: return 'success';
        case SourceFileType.Ldf: return 'warning';
        default: return '';
      }
    };

    const getStatusTagType = (status: SourceFileStatus) => {
      switch (status) {
        case SourceFileStatus.Pending: return '';
        case SourceFileStatus.Parsing: return 'warning';
        case SourceFileStatus.Parsed: return 'success';
        case SourceFileStatus.Error: return 'danger';
        default: return '';
      }
    };

    const getStatusText = (status: SourceFileStatus) => {
      switch (status) {
        case SourceFileStatus.Pending: return '待解析';
        case SourceFileStatus.Parsing: return '解析中';
        case SourceFileStatus.Parsed: return '已解析';
        case SourceFileStatus.Error: return '解析失败';
        default: return status;
      }
    };

    // 暴露给父组件的方法
    const refreshDataFiles = () => {
      loadDataFiles();
    };

    // 生命周期
    onMounted(() => {
      loadDataFiles();
    });

    return {
      dataFiles,
      selectDataFiles,
      parseDataFile,
      viewDataFileDetails,
      openDataFileFolder,
      removeDataFile,
      clearAllDataFiles,
      getFileTypeTagType,
      getStatusTagType,
      getStatusText,
      refreshDataFiles,
      // 枚举
      SourceFileStatus
    };
  },
});
</script>

<style scoped>
/* 参数数据文件样式 */
.data-files-section {
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  flex-shrink: 0;
  max-height: 300px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.data-files-table {
  margin-top: 12px;
}

.data-files-table .file-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-files-table .file-name .name {
  font-weight: 500;
}

.folder-button {
  color: var(--el-color-primary) !important;
  padding: 2px !important;
  min-height: auto !important;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.folder-button:hover {
  color: var(--el-color-primary-dark-2) !important;
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}
</style>
