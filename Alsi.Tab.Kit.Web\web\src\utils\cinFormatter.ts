/**
 * CIN 格式参数值智能格式化工具
 */

/**
 * 智能格式化 CIN 格式参数值
 * @param content 要格式化的字符串内容
 * @param level 缩进层级 (0-6)，0表示压缩为一行
 * @returns 格式化后的字符串
 */
export function formatCinValue(content: string, level: number): string {
  if (!content || !content.trim()) {
    return content;
  }

  const trimmedContent = content.trim();

  // 格式化等级为 0 时，压缩为一行
  if (level === 0) {
    return trimmedContent
      .replace(/\s+/g, ' ')
      .replace(/\s*{\s*/g, '{')
      .replace(/\s*}\s*/g, '}')
      .replace(/\s*,\s*/g, ',')
      .trim();
  }

  // 不是复杂格式（不包含大括号），直接返回
  if (!trimmedContent.includes('{') || !trimmedContent.includes('}')) {
    return trimmedContent;
  }

  // 解析并格式化 CIN 格式的结构体
  let currentLevel = 0;
  let result = '';
  let i = 0;

  while (i < trimmedContent.length) {
    const char = trimmedContent[i];
    
    if (char === '{') {
      result += '{';
      currentLevel++;
      
      // 如果当前层级小于等于格式化等级，换行并缩进
      if (currentLevel <= level) {
        result += '\n' + '  '.repeat(currentLevel);
      }
    } else if (char === '}') {
      currentLevel--;
      
      // 如果当前层级小于格式化等级，换行并缩进
      if (currentLevel < level) {
        result += '\n' + '  '.repeat(currentLevel);
      }
      result += '}';
    } else if (char === ',') {
      result += ',';
      
      // 如果当前层级小于等于格式化等级，换行并缩进
      if (currentLevel <= level) {
        result += '\n' + '  '.repeat(currentLevel);
      } else {
        result += ' '; // 同一行用空格分隔
      }
    } else if (char === ' ' || char === '\n' || char === '\r' || char === '\t') {
      // 跳过原有的空白字符，格式化时重新添加
      while (i + 1 < trimmedContent.length && /\s/.test(trimmedContent[i + 1])) {
        i++;
      }
    } else {
      result += char;
    }
    
    i++;
  }

  // 清理多余的空行和空格
  return result
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+$/gm, '')
    .trim();
}

/**
 * 自动检测 CIN 值的当前格式化层级
 * @param content CIN 格式的字符串内容
 * @returns 检测到的格式化层级 (0-6)
 */
export function detectCinFormatLevel(content: string): number {
  if (!content || !content.trim()) {
    return 0;
  }

  const trimmedContent = content.trim();
  
  // 不包含大括号的简单值
  if (!trimmedContent.includes('{') || !trimmedContent.includes('}')) {
    return 0;
  }

  const lines = trimmedContent.split('\n');
  
  // 单行内容
  if (lines.length === 1) {
    return 0;
  }

  // 计算最大缩进层级
  let maxLevel = 0;
  lines.forEach(line => {
    const match = line.match(/^(\s*)/);
    if (match) {
      const indentLevel = Math.floor(match[1].length / 2);
      maxLevel = Math.max(maxLevel, indentLevel);
    }
  });

  return Math.min(maxLevel, 6);
}
