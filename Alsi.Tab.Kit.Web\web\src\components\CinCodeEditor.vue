<template>
  <div class="cin-code-editor">
    <div ref="editorRef" class="editor-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { EditorView, basicSetup } from 'codemirror';
import { EditorState, Extension, Compartment } from '@codemirror/state';
import { cpp } from '@codemirror/lang-cpp';

// Props
interface Props {
  modelValue: string;
  readonly?: boolean;
  placeholder?: string;
  height?: string;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  placeholder: '',
  height: '300px'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  'change': [value: string];
}>();

// 数据
const editorRef = ref<HTMLElement | null>(null);
let editorView: EditorView | null = null;
const readOnlyCompartment = new Compartment();

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value) return;

  const extensions: Extension[] = [
    basicSetup,
    cpp(), // 使用 C/C++ 语法高亮
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        const value = update.state.doc.toString();
        emit('update:modelValue', value);
        emit('change', value);
      }
    }),
    readOnlyCompartment.of(EditorState.readOnly.of(props.readonly))
  ];

  const state = EditorState.create({
    doc: props.modelValue,
    extensions
  });

  editorView = new EditorView({
    state,
    parent: editorRef.value
  });

  // 设置编辑器高度
  if (editorRef.value) {
    editorRef.value.style.height = props.height;
  }
};

// 更新编辑器内容
const updateContent = (value: string) => {
  if (editorView && editorView.state.doc.toString() !== value) {
    editorView.dispatch({
      changes: {
        from: 0,
        to: editorView.state.doc.length,
        insert: value
      }
    });
  }
};

// 监听器
watch(() => props.modelValue, (newValue) => {
  updateContent(newValue);
});

watch(() => props.readonly, (newReadonly) => {
  if (editorView) {
    editorView.dispatch({
      effects: readOnlyCompartment.reconfigure(EditorState.readOnly.of(newReadonly))
    });
  }
});

// 生命周期
onMounted(async () => {
  await nextTick();
  initEditor();
});

onUnmounted(() => {
  if (editorView) {
    editorView.destroy();
    editorView = null;
  }
});

// 暴露方法
defineExpose({
  focus: () => {
    if (editorView) {
      editorView.focus();
    }
  },
  blur: () => {
    if (editorView) {
      editorView.contentDOM.blur();
    }
  }
});
</script>

<style scoped>
.cin-code-editor {
  width: 100%;
  height: 100%;
}

.editor-container {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.editor-container :deep(.cm-editor) {
  height: 100%;
}

.editor-container :deep(.cm-scroller) {
  height: 100%;
  font-family: 'consolas' 'Courier New', Courier, monospace;
}

</style>
