using Alsi.Common.Parsers.Cin;

namespace Alsi.Tab.Kit.UnitTests;

public class CinParser_ReplaceValueTests
{
    public CinParser_ReplaceValueTests()
    {
        CinParser.Initialize();
    }

    [Fact]
    public void ReplaceVariableValue_ShouldReplaceSimpleVariable()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    const dword TEST_VALUE = 0x123;
    int simpleVar = 42;
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    const dword TEST_VALUE = 0x123;
    int simpleVar = 999;
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "simpleVar", "999");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }

    [Fact]
    public void ReplaceVariableValue_ShouldReplaceArrayVariable()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    dword testArray[3] = {1,2,3};
    char stringArray[10] = ""hello"";
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    dword testArray[3] = {10,20,30};
    char stringArray[10] = ""hello World"";
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "testArray", "{10,20,30}");
        result = CinParser.ReplaceVariableValue(result, "stringArray", @"""hello World""");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }

    [Fact]
    public void ReplaceVariableValue_ShouldReplaceStructArray()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    struct TestStruct myStruct[2] = {
        {1,2},
        {3,4}
    };
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    struct TestStruct myStruct[2] = {
        {10,20},

        {30,40}
    };
}";

        var newValue = @"{
        {10,20},

        {30,40}
    }";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "myStruct", newValue);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }

    [Fact]
    public void ReplaceVariableValue_ShouldPreserveComments()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    int testVar = 100;  // This is a test variable
    dword anotherVar = 0x200; // Another comment
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    int testVar = 500;  // This is a test variable
    dword anotherVar = 0x200; // Another comment
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "testVar", "500");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }

    [Fact]
    public void ReplaceVariableValue_ShouldReturnNullForEmptyContent()
    {
        // Act
        var result = CinParser.ReplaceVariableValue("", "testVar", "123");

        // Assert
        result.ShouldBeEmpty();
    }

    [Fact]
    public void ReplaceVariableValue_ShouldHandleComplexMultiLineStructArray()
    {
        // Arrange - 测试复杂的多行结构体数组，确保完全替换
        var testContent = @"/*@!Encoding:936*/
variables{
    struct STRU_SID_SUBID supportSIDandSubIDs[20] = {
        {7,0x10,3,{1,2,3}},
        {7,0x11,1,{1}},
        {7,0x14},{7,0x19},
        {4,0x27,2,{1,2}},
        {2,0x27,2,{0x11,0x12}},
        {7,0x22}
    };
    int otherVar = 42;
}";

        var newValue = @"{
        {1,0x20,2,{10,20}},
        {2,0x21,1,{30}}
    }";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    struct STRU_SID_SUBID supportSIDandSubIDs[20] = {
        {1,0x20,2,{10,20}},
        {2,0x21,1,{30}}
    };
    int otherVar = 42;
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "supportSIDandSubIDs", newValue);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);

        // 确保没有残留的旧数据
        result.ShouldNotContain("{7,0x10,3,{1,2,3}}");
        result.ShouldNotContain("{7,0x11,1,{1}}");
        result.ShouldNotContain("{7,0x14}");
    }

    [Fact]
    public void ReplaceVariableValue_ShouldReplaceConstVariable()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    const dword CONST_VALUE = 0x123;
    int normalVar = 456;
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    const dword CONST_VALUE = 0x999;
    int normalVar = 456;
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "CONST_VALUE", "0x999");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }

    [Fact]
    public void ReplaceVariableValue_ShouldHandleStringValues()
    {
        // Arrange
        var testContent = @"/*@!Encoding:936*/
variables{
    char ECU_NAME_STR[40]=""BasicDiagnosticsEcu"";
    int someNumber = 123;
}";

        var expectedContent = @"/*@!Encoding:936*/
variables{
    char ECU_NAME_STR[40]=""NewEcuName"";
    int someNumber = 123;
}";

        // Act
        var result = CinParser.ReplaceVariableValue(testContent, "ECU_NAME_STR", "\"NewEcuName\"");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedContent);
    }
}
