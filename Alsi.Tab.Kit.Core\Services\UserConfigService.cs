using Alsi.App;
using Alsi.Common.Utils;
using System;
using System.IO;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services
{
    public class UserConfig
    {
        public CinParametersConfig CinParametersConfig { get; set; } = new CinParametersConfig();
    }

    public class CinParametersConfig
    {
        public string[] SourceFilePaths { get; set; } = Array.Empty<string>();
    }

    public class UserConfigService
    {
        public static UserConfigService Instance = new UserConfigService();

        private const string UserConfigFileName = "user_config.json";

        private readonly string _userConfigFilePath;
        private readonly object _lockObject = new object();

        public UserConfigService()
        {
            var appDataPath = GetAppDataFolder();
            _userConfigFilePath = Path.Combine(appDataPath, UserConfigFileName);
            Load();
        }

        private string GetAppDataFolder()
        {
            var dataFolder = Path.Combine(AppEnv.WebHostApp.DataFolder, "app_data");
            Directory.CreateDirectory(dataFolder);
            return dataFolder;
        }

        private UserConfig Load()
        {
            lock (_lockObject)
            {
                if (File.Exists(_userConfigFilePath))
                {
                    var jsonContent = File.ReadAllText(_userConfigFilePath);
                    var userConfig = JsonUtils.Deserialize<UserConfig>(jsonContent);
                    return userConfig ?? new UserConfig();
                }
                return new UserConfig();
            }
        }

        private void Save(UserConfig userConfig)
        {
            lock (_lockObject)
            {
                var jsonContent = JsonUtils.Serialize(userConfig, true);
                File.WriteAllText(_userConfigFilePath, jsonContent);
            }
        }

        public string[] GeSourceFilePaths()
        {
            lock (_lockObject)
            {
                var userConfig = Load();
                return userConfig.CinParametersConfig.SourceFilePaths;
            }
        }

        public void SaveSourceFilePaths(string[] filePaths)
        {
            var userConfig = Load();
            userConfig.CinParametersConfig.SourceFilePaths = filePaths.ToArray();
            Save(userConfig);
        }

        public void AddSourceFilePath(string filePath)
        {
            var userConfig = Load();
            if (!userConfig.CinParametersConfig.SourceFilePaths.Any(
                x => x.Equals(filePath, StringComparison.OrdinalIgnoreCase)))
            {
                var list = userConfig.CinParametersConfig.SourceFilePaths.ToList();
                list.Add(filePath);
                userConfig.CinParametersConfig.SourceFilePaths = list.ToArray();
            }

            Save(userConfig);
        }

        public void RemoveSourceFilePath(string filePath)
        {
            var userConfig = Load();
            var list = userConfig.CinParametersConfig.SourceFilePaths.ToList();
            list.RemoveAll(x => x.Equals(filePath, StringComparison.OrdinalIgnoreCase));
            userConfig.CinParametersConfig.SourceFilePaths = list.ToArray();
            Save(userConfig);
        }

        public void RemoveAllSourceFilePath()
        {
            var userConfig = Load();
            userConfig.CinParametersConfig.SourceFilePaths = Array.Empty<string>();
            Save(userConfig);
        }
    }
}
