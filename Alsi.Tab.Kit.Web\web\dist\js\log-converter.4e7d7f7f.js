"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[488],{9936:function(e,l,s){s.r(l),s.d(l,{default:function(){return R}});var a=s(6768),t=s(4232);const o={class:"log-converter"},r={class:"converter-content"},n={class:"config-row"},i={class:"config-item",style:{width:"80%"}},c={class:"config-row"},u={class:"config-item"},g={class:"config-item"},p={key:0,class:"config-item"},d={class:"action-buttons"},v={key:0,class:"progress-section"},f={class:"current-operation"},k={key:0,class:"file-progress"},m={class:"file-progress-list"},b={class:"file-name"},C={class:"file-status"},F={class:"file-actions"},h={class:"status-bar"},P={key:0},L={key:1},_={key:2,class:"processing-status"};function y(e,l,s,y,w,K){const V=(0,a.g2)("el-button"),E=(0,a.g2)("el-input"),Q=(0,a.g2)("el-radio"),R=(0,a.g2)("el-radio-group"),S=(0,a.g2)("el-switch"),x=(0,a.g2)("el-input-number"),X=(0,a.g2)("el-progress"),q=(0,a.g2)("font-awesome-icon");return(0,a.uX)(),(0,a.CE)("div",o,[l[14]||(l[14]=(0,a.Lk)("div",{class:"title-bar"},"Log 转换工具",-1)),(0,a.Lk)("div",r,[(0,a.Lk)("div",n,[(0,a.Lk)("div",i,[l[5]||(l[5]=(0,a.Lk)("label",{class:"config-label",style:{"margin-bottom":"10px"}},"Log 文件路径:",-1)),(0,a.bF)(E,{modelValue:e.fileForm.filePath,"onUpdate:modelValue":l[0]||(l[0]=l=>e.fileForm.filePath=l),placeholder:"请选择一个 Log 文件或输入路径，支持格式：.asc .blf",onBlur:e.analyzeFile},{append:(0,a.k6)(()=>[(0,a.bF)(V,{onClick:e.selectFile},{default:(0,a.k6)(()=>l[4]||(l[4]=[(0,a.eW)(" 选择 Log 文件 ")])),_:1,__:[4]},8,["onClick"])]),_:1},8,["modelValue","onBlur"])])]),(0,a.Lk)("div",c,[(0,a.Lk)("div",u,[l[8]||(l[8]=(0,a.Lk)("label",{class:"config-label"},"目标格式",-1)),(0,a.bF)(R,{modelValue:e.processRequest.targetFormat,"onUpdate:modelValue":l[1]||(l[1]=l=>e.processRequest.targetFormat=l)},{default:(0,a.k6)(()=>[(0,a.bF)(Q,{value:1},{default:(0,a.k6)(()=>l[6]||(l[6]=[(0,a.eW)("ASC")])),_:1,__:[6]}),(0,a.bF)(Q,{value:2},{default:(0,a.k6)(()=>l[7]||(l[7]=[(0,a.eW)("BLF")])),_:1,__:[7]})]),_:1},8,["modelValue"])]),(0,a.Lk)("div",g,[l[9]||(l[9]=(0,a.Lk)("label",{class:"config-label"},"启用分割",-1)),(0,a.bF)(S,{modelValue:e.processRequest.enableSplit,"onUpdate:modelValue":l[2]||(l[2]=l=>e.processRequest.enableSplit=l),onChange:e.onSplitToggle},null,8,["modelValue","onChange"])]),e.processRequest.enableSplit?((0,a.uX)(),(0,a.CE)("div",p,[l[10]||(l[10]=(0,a.Lk)("label",{class:"config-label"},"分割文件数",-1)),(0,a.bF)(x,{modelValue:e.splitFileCount,"onUpdate:modelValue":l[3]||(l[3]=l=>e.splitFileCount=l),min:1,max:100,onChange:e.onSplitCountChange,class:"split-count-input"},null,8,["modelValue","onChange"])])):(0,a.Q3)("",!0)]),(0,a.Lk)("div",d,[(0,a.bF)(V,{type:"primary",disabled:!e.fileForm.filePath,onClick:e.startProcess,loading:e.isProcessing},{default:(0,a.k6)(()=>l[11]||(l[11]=[(0,a.eW)(" 开始转换 ")])),_:1,__:[11]},8,["disabled","onClick","loading"]),(0,a.bF)(V,{type:"danger",disabled:!e.isProcessing,onClick:e.cancelProcess},{default:(0,a.k6)(()=>l[12]||(l[12]=[(0,a.eW)(" 取消转换 ")])),_:1,__:[12]},8,["disabled","onClick"])]),e.progress?((0,a.uX)(),(0,a.CE)("div",v,[l[13]||(l[13]=(0,a.Lk)("h4",null,"转换进度",-1)),(0,a.bF)(X,{percentage:e.progress.overallProgressPercentage,status:e.progress.isCompleted?"success":"active"},null,8,["percentage","status"]),(0,a.Lk)("p",f,(0,t.v_)(e.progress.currentOperation),1),e.progress.fileProgresses&&e.progress.fileProgresses.length>0?((0,a.uX)(),(0,a.CE)("div",k,[(0,a.Lk)("div",m,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(e.progress.fileProgresses,(l,s)=>((0,a.uX)(),(0,a.CE)("div",{key:s,class:"file-progress-item"},[(0,a.Lk)("div",b,(0,t.v_)(l.fileName),1),(0,a.bF)(X,{percentage:l.progressPercentage,"show-text":!1,size:"small",style:{width:"100px"}},null,8,["percentage"]),(0,a.Lk)("div",C,(0,t.v_)(e.getStatusName(l.status)),1),(0,a.Lk)("div",F,["Completed"===l.status?((0,a.uX)(),(0,a.Wv)(V,{key:0,type:"text",size:"small",onClick:s=>e.openFileInExplorer(l.filePath),class:"explorer-button",title:"在文件夹中显示"},{default:(0,a.k6)(()=>[(0,a.bF)(q,{icon:"folder-open"})]),_:2},1032,["onClick"])):(0,a.Q3)("",!0)])]))),128))])])):(0,a.Q3)("",!0)])):(0,a.Q3)("",!0)]),(0,a.Lk)("div",h,[e.fileForm.filePath?((0,a.uX)(),(0,a.CE)("span",P," 文件: 已选择 - "+(0,t.v_)(e.fileForm.filePath),1)):(0,a.Q3)("",!0),e.fileForm.filePath?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("span",L,"请选择 Log 文件")),e.isProcessing?((0,a.uX)(),(0,a.CE)("span",_," 转换中... "+(0,t.v_)(e.progress?.overallProgressPercentage||0)+"% ",1)):(0,a.Q3)("",!0)])])}var w=s(144),K=s(1021),V=(0,a.pM)({name:"LogConverterView",setup(){const e=(0,w.KR)({filePath:""}),l=(0,w.KR)({sourceFilePath:"",targetFormat:K.Mo.Blf,enableSplit:!1,splitFileCount:1}),s=(0,w.KR)(1),t=(0,w.KR)(null),o=(0,w.KR)(null),r=(0,w.KR)(!1);let n=null;const i=async()=>{const s=await K.GQ.dataLogConvert.selectFile(),a=s.data;a&&(e.value.filePath=a,l.value.sourceFilePath=a)},c=async()=>{e.value.filePath&&(l.value.sourceFilePath=e.value.filePath)},u=()=>{l.value.enableSplit&&(s.value=1,l.value.splitFileCount=1)},g=()=>{l.value.splitFileCount=s.value},p=async()=>{r.value=!0;try{const e=await K.GQ.dataLogConvert.startProcess(l.value);t.value=e.data,d()}catch(e){console.error("开始处理失败:",e),r.value=!1}},d=()=>{t.value&&(n=window.setInterval(async()=>{try{const e=await K.GQ.dataLogConvert.getProgress(t.value);o.value=e.data,o.value?.isCompleted&&(v(),r.value=!1)}catch(e){console.error("获取进度失败:",e)}},1e3))},v=()=>{n&&(clearInterval(n),n=null)},f=async()=>{if(t.value)try{await K.GQ.dataLogConvert.cancelProcess(t.value),v(),r.value=!1}catch(e){console.error("取消处理失败:",e)}},k=e=>{switch(e){case K.KK.Pending:return"等待中";case K.KK.Processing:return"处理中";case K.KK.Completed:return"已完成";case K.KK.Failed:return"失败";case K.KK.Cancelled:return"已取消";default:return"未知"}},m=async e=>{await K.GQ.explorer.openExplorer(e)};return(0,a.hi)(()=>{v()}),{fileForm:e,processRequest:l,splitFileCount:s,progress:o,isProcessing:r,selectFile:i,analyzeFile:c,onSplitToggle:u,onSplitCountChange:g,startProcess:p,cancelProcess:f,getStatusName:k,openFileInExplorer:m}}}),E=s(1241);const Q=(0,E.A)(V,[["render",y],["__scopeId","data-v-374b5428"]]);var R=Q}}]);
//# sourceMappingURL=log-converter.4e7d7f7f.js.map