<template>
  <el-dialog v-model="visible" :title="dialogTitle" width="80%" :before-close="handleClose" destroy-on-close>
    <div class="dialog-content">
      <!-- ECU选择和搜索 -->
      <div class="filter-section">
        <el-select v-model="selectedEcu" size="small" placeholder="选择 ECU" style="width: 100px;"
          @change="handleEcuChange">
          <el-option v-for="ecu in ecuList" :key="ecu" :label="ecu" :value="ecu" />
        </el-select>

        <el-input v-model="searchText" placeholder="搜索参数名称..." clearable size="small" style="width: 260px">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>

        <div class="selection-info">
          已选择 {{ selectedParams.length }}/{{ availableParamsCount }} 个参数
        </div>
      </div>

      <!-- 参数列表 -->
      <div class="param-list">
        <el-table ref="paramTable" :data="filteredParams" height="400" @selection-change="handleSelectionChange"
          row-key="name" size="small">
          <el-table-column type="selection" width="55" :selectable="isParamSelectable" />
          <el-table-column prop="name" label="参数名" width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="name-cell">
                <el-tooltip v-if="!isParamInCinList(row.name)" content="此参数在 CIN 参数列表中不存在，无法应用" placement="top">
                  <el-icon class="warning-icon">
                    <Warning />
                  </el-icon>
                </el-tooltip>
                <span>
                  {{ row.name }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="paramType" label="数据类型" width="200" show-overflow-tooltip />
          <el-table-column prop="value" label="参数值" min-width="200">
            <template #default="{ row }">
              <span class="param-value-text" :title="formatValue(row.value)">
                {{ formatValue(row.value) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="showParamValue(row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>


    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="applySelectedParams" :disabled="selectedParams.length === 0">
          应用参数 ({{ selectedParams.length }})
        </el-button>
      </div>
    </template>

    <!-- 参数值查看弹窗 -->
    <ParamValueViewer v-model="paramValueDialogVisible" :value="currentParamValue" :readonly="true" title="参数值" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Warning } from '@element-plus/icons-vue';
import { SourceFile, ParsedParam, SourceFileType, ParamType } from '@/api/appApi';
import ParamValueViewer from './ParamValueViewer.vue';

// Props
interface Props {
  modelValue: boolean;
  sourceFile: SourceFile | null;
  parsedParams: ParsedParam[];
  cinParams: { name: string }[]; // CIN 参数列表
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'apply-params': [params: ParsedParam[]];
}>();

// 数据
const visible = ref(false);
const searchText = ref('');
const selectedEcu = ref('');
const selectedParams = ref<ParsedParam[]>([]);
const paramValueDialogVisible = ref(false);
const currentParamValue = ref<any>('');
const paramTable = ref();

// 计算属性
const ecuList = computed(() => {
  const ecus = new Set(props.parsedParams.map(p => p.ecuName));
  return Array.from(ecus).sort();
});

const dialogTitle = computed(() => {
  if (!props.sourceFile) {
    return '参数解析结果';
  }

  const fileName = props.sourceFile.fileName;
  return `参数解析结果 - ${fileName}`;
});

const filteredParams = computed(() => {
  let params = props.parsedParams;

  // 按ECU筛选
  if (selectedEcu.value) {
    params = params.filter(p => p.ecuName === selectedEcu.value);
  }

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    params = params.filter(p =>
      p.name?.toLowerCase().includes(search) ||
      (p.description && p.description.toLowerCase().includes(search))
    );
  }

  // 排序：能勾选的参数排在前面
  params.sort((a, b) => {
    const aSelectable = isParamInCinList(a.name);
    const bSelectable = isParamInCinList(b.name);
    
    // 如果一个可选，一个不可选，可选的排在前面
    if (aSelectable && !bSelectable) return -1;
    if (!aSelectable && bSelectable) return 1;
    
    // 如果都可选或都不可选，按参数名字母顺序排序
    return a.name.localeCompare(b.name);
  });

  return params;
});

// 计算可用参数数量
const availableParamsCount = computed(() => {
  return filteredParams.value.filter(param => isParamInCinList(param.name)).length;
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 重置状态
    searchText.value = '';
    selectedParams.value = [];
    // 默认选择第一个ECU
    if (ecuList.value.length > 0) {
      selectedEcu.value = ecuList.value[0];
      // 默认全选当前ECU的参数
      nextTick(() => {
        selectAllCurrentEcuParams();
      });
    }
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(selectedEcu, () => {
  // ECU切换时默认全选当前ECU的参数
  nextTick(() => {
    selectAllCurrentEcuParams();
  });
});

// 方法
// 检查参数是否在 CIN 参数列表中（不区分大小写）
const isParamInCinList = (paramName: string): boolean => {
  if (!props.cinParams || props.cinParams.length === 0) {
    return false; // 如果没有 CIN 参数列表，则所有参数都不可选
  }
  return props.cinParams.some(cinParam =>
    cinParam.name.toLowerCase() === paramName.toLowerCase()
  );
};

// 检查参数是否可以被选择
const isParamSelectable = (row: ParsedParam): boolean => {
  return isParamInCinList(row.name);
};

const handleEcuChange = () => {
  // 切换ECU时默认选择可用的参数
  selectAllCurrentEcuParams();
};

const selectAllCurrentEcuParams = () => {
  // 只选择当前ECU中在CIN参数列表中存在的参数
  if (paramTable.value) {
    const currentEcuParams = filteredParams.value;
    currentEcuParams.forEach(row => {
      const shouldSelect = isParamInCinList(row.name);
      paramTable.value.toggleRowSelection(row, shouldSelect);
    });
  }
};

const handleClose = () => {
  visible.value = false;
};

const handleSelectionChange = (selection: ParsedParam[]) => {
  selectedParams.value = selection;
};

const formatValue = (value: any) => {
  if (value === null || value === undefined) {
    return '';
  }

  return String(value);
};

const showParamValue = (param: ParsedParam) => {
  currentParamValue.value = param.value;
  paramValueDialogVisible.value = true;
};

const applySelectedParams = () => {
  if (selectedParams.value.length === 0) {
    ElMessage.warning('请先选择要应用的参数');
    return;
  }

  emit('apply-params', selectedParams.value);
  ElMessage.success(`成功应用 ${selectedParams.value.length} 个参数`);
  handleClose();
};
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.param-list {
  border: 1px solid var(--el-border-color-base);
  border-radius: 6px;
}

.selection-info {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-left: auto;
}

.param-value-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.warning-icon {
  margin-left: 4px;
  font-size: 12px;
  color: gray
}
</style>
