<template>
  <div id="app">
    <!-- 侧边菜单 -->
    <div class="sidebar" :class="{ collapsed: isMenuCollapsed }">
      <!-- 菜单头 -->
      <div class="menu-header">
        <div class="logo-container" v-if="!isMenuCollapsed">
          <router-link to="/">
            <img src="@/assets/logo.svg" alt="Logo" class="logo" />
          </router-link>
          <span class="app-name">TabKit</span>
        </div>
        <div class="logo-container-collapsed" v-else>
          <router-link to="/">
            <img src="@/assets/logo.svg" alt="Logo" class="logo-small" />
          </router-link>
        </div>
      </div>

      <!-- 菜单项 -->
      <div class="menu-items">
        <!-- 主页 -->
        <router-link to="/" class="menu-item" active-class="active">
          <font-awesome-icon icon="home" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">主页</span>
        </router-link>

        <!-- 动态顶部菜单项 -->
        <component
          v-for="app in topMenuApps"
          :key="app.id"
          :is="app.appType === AppType.Internal ? 'router-link' : 'div'"
          :to="app.appType === AppType.Internal ? app.appUrl : undefined"
          class="menu-item"
          :class="{ 'external-app': app.appType === AppType.External }"
          active-class="active"
          @click="handleMenuClick(app, $event)"
        >
          <!-- 图标：支持文件图标和前端图标 -->
          <img
            v-if="app.iconExists && hasFileExtension(app.icon)"
            :src="getAppIconUrl(app.id)"
            :alt="app.name"
            class="menu-icon-img"
            @error="handleIconError"
          />
          <font-awesome-icon 
            v-else-if="app.icon && !hasFileExtension(app.icon)"
            :icon="app.icon" 
            class="menu-icon" 
          />
          <font-awesome-icon 
            v-else
            icon="cube" 
            class="menu-icon" 
          />
          <span v-if="!isMenuCollapsed" class="menu-text">{{ app.name }}</span>
        </component>
      </div>

      <!-- 底部菜单项 -->
      <div class="menu-bottom">
        <!-- 动态底部菜单项 -->
        <component
          v-for="app in bottomMenuApps"
          :key="app.id"
          :is="app.appType === AppType.Internal ? 'router-link' : 'div'"
          :to="app.appType === AppType.Internal ? app.appUrl : undefined"
          class="menu-item"
          :class="{ 'external-app': app.appType === AppType.External }"
          active-class="active"
          @click="handleMenuClick(app, $event)"
        >
          <!-- 图标：支持文件图标和前端图标 -->
          <img
            v-if="app.iconExists && hasFileExtension(app.icon)"
            :src="getAppIconUrl(app.id)"
            :alt="app.name"
            class="menu-icon-img"
            @error="handleIconError"
          />
          <font-awesome-icon 
            v-else-if="app.icon && !hasFileExtension(app.icon)"
            :icon="app.icon" 
            class="menu-icon" 
          />
          <font-awesome-icon 
            v-else
            icon="cube" 
            class="menu-icon" 
          />
          <span v-if="!isMenuCollapsed" class="menu-text">{{ app.name }}</span>
        </component>

        <!-- 菜单切换按钮 -->
        <div class="menu-toggle-section">
          <div class="menu-toggle" @click="toggleMenu">
            <font-awesome-icon :icon="isMenuCollapsed ? 'chevron-right' : 'chevron-left'" />
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ expanded: isMenuCollapsed }">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { appApi, AppEntry, AppType } from '@/api/appApi';
import { ElMessageBox } from 'element-plus';

export default defineComponent({
  name: 'App',
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const isMenuCollapsed = ref(false);
    const menuApps = ref<AppEntry[]>([]);

    const toggleMenu = () => {
      isMenuCollapsed.value = !isMenuCollapsed.value;
    };

    // 计算属性：顶部菜单应用（包含内部和外部应用）
    const topMenuApps = computed(() => {
      return menuApps.value
        .filter(app => app.showInTopMenu)
        .sort((a, b) => a.displayOrder - b.displayOrder);
    });

    // 计算属性：底部菜单应用（包含内部和外部应用）
    const bottomMenuApps = computed(() => {
      return menuApps.value
        .filter(app => app.showInBottomMenu)
        .sort((a, b) => a.displayOrder - b.displayOrder);
    });

    // 加载菜单应用配置
    const loadMenuApps = async () => {
      try {
        const response = await appApi.appConfig.getList();
        menuApps.value = response.data;
      } catch (error) {
        console.error('加载菜单应用配置失败:', error);
      }
    };

    // 处理菜单项点击
    const handleMenuClick = async (app: AppEntry, event: Event) => {
      if (app.appType === AppType.External) {
        // 外部应用：阻止默认导航行为，直接启动外部程序
        event.preventDefault();
        await launchApp(app);
      }
      // 内部应用：允许默认的路由导航
    };

    // 启动应用程序
    const launchApp = async (app: AppEntry) => {
      if (!app.exeExists) {
        // 这里可以使用 Element Plus 的消息提示，如果可用的话
        console.error(`应用 "${app.name}" 的可执行文件不存在`);
        return;
      }

      await ElMessageBox.confirm(`是否启动 "${app.name}" ?`, '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      });

      try {
        await appApi.appConfig.launch({ appId: app.id });
        console.log(`正在启动 "${app.name}"`);
      } catch (error: any) {
        console.error('启动应用程序失败:', error);
        console.error(error.response?.data?.message || `启动 "${app.name}" 失败`);
      }
    };

    // 判断是否为文件图标（有文件扩展名）
    const hasFileExtension = (filename: string): boolean => {
      if (!filename) return false;
      const dotIndex = filename.lastIndexOf('.');
      return dotIndex > 0 && dotIndex < filename.length - 1;
    };

    // 获取应用图标 URL
    const getAppIconUrl = (appId: string): string => {
      return appApi.appConfig.getIconUrl(appId);
    };

    // 处理图标加载错误
    const handleIconError = (event: Event) => {
      const target = event.target as HTMLImageElement;
      target.style.display = 'none';
    };

    onMounted(() => {
      loadMenuApps();
    });

    return {
      isMenuCollapsed,
      toggleMenu,
      topMenuApps,
      bottomMenuApps,
      handleMenuClick,
      hasFileExtension,
      getAppIconUrl,
      handleIconError,
      AppType,
    };
  },
});
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  display: flex;
  width: 100vw;
}

.sidebar {
  width: 200px;
  background-color: white;
  color: #333;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  border-right: 1px solid #e4e7ed;

  &.collapsed {
    width: 60px;
  }

  .menu-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    border-bottom: 1px solid #e4e7ed;
    background-color: white;

    .logo-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo {
        width: 36px;
        height: 36px;
      }

      .app-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-right: 40px;
      }
    }

    .logo-container-collapsed {
      display: flex;
      justify-content: center;
      width: 100%;

      .logo-small {
        width: 32px;
        height: 32px;
      }
    }
  }

  .menu-toggle-section {
    padding: 8px 12px;
    border-top: 1px solid #e4e7ed;
    height: 44px;

    .menu-toggle {
      width: 100%;
      height: 36px;
      background-color: #f5f7fa;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e4e7ed;
        color: #333;
      }
    }
  }

  &.collapsed .menu-toggle-section {
    padding: 8px 6px;

    .menu-toggle {
      height: 32px;
      border-radius: 4px;
    }
  }

  .menu-items {
    flex: 1;
    padding-top: 10px;
  }

  .menu-bottom {
    padding-bottom: 10px;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    margin: 2px 8px;
    border-radius: 6px;
    height: 44px;

    &:hover {
      background-color: #f5f7fa;
      color: #333;
    }

    &.active {
      background-color: #e6f7ff;
      color: var(--el-color-primary);
      border-left-color: var(--el-color-primary);
    }

    // 外部应用样式：可点击但不会激活
    &.external-app {
      cursor: pointer;
      
      &:hover {
        background-color: #f5f7fa;
        color: #333;
      }
      
      // 外部应用不应用激活样式
      &.active {
        background-color: #f5f7fa;
        color: #333;
        border-left-color: transparent;
      }
    }

    .menu-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .menu-icon-img {
      width: 20px;
      height: 20px;
      object-fit: contain;
      border-radius: 2px;
    }

    .menu-text {
      margin-left: 12px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      font-weight: 500;
    }
  }

  &.collapsed .menu-item {
    justify-content: center;
    padding: 12px 10px;
    margin: 2px 4px;

    .menu-icon {
      margin: 0;
    }
  }
}

.main-content {
  flex: 1;
  background-color: var(--el-fill-color-base);
  overflow-y: auto;
  transition: margin-left 0.3s ease;

  &.expanded {
    margin-left: 0;
  }
}
</style>
