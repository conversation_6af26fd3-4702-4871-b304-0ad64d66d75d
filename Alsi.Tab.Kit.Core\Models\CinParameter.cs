using Alsi.Common.Parsers.Cin.Models;
using Alsi.Tab.Kit.Core.Services.Capl;
using System;
using System.Collections.Generic;

namespace Alsi.Tab.Kit.Core.Models
{
    public class CinParameterRequest
    {
        // 源文件类型：template（从模板选择）或 file（从文件选择）
        public string SourceType { get; set; } = string.Empty;

        public Guid? TemplateId { get; set; }

        public string FilePath { get; set; } = string.Empty;

        public Dictionary<string, string> ParameterValues { get; set; } = new Dictionary<string, string>();
    }

    public class CinParameterParseResponse
    {
        public string SourceFilePath { get; set; } = string.Empty;

        public List<CinVariable> Variables { get; set; } = new List<CinVariable>();

        public int CodePage { get; set; }
    }

    public class CinParameterProcessResponse
    {
        public string OutputFilePath { get; set; } = string.Empty;
    }
}
