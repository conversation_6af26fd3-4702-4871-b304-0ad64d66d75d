using Alsi.Tab.Kit.Core.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Tab.Kit.Core.Services
{
    public class SourceFileService
    {
        private static SourceFileParamService _paramService = new SourceFileParamService();
        private static List<SourceFile> _sourceFiles = new List<SourceFile>();
        private static readonly object _lockObject = new object();

        public SourceFileService()
        {
            // 初始化上次的文件列表
            var filePaths = UserConfigService.Instance.GeSourceFilePaths();
            AddSourceFiles(filePaths);
        }

        private void SyncUserConfig()
        {
            UserConfigService.Instance.SaveSourceFilePaths(
                _sourceFiles.Select(x => x.Path).ToArray());
        }

        public void AddSourceFiles(string[] filePaths)
        {
            lock (_lockObject)
            {
                foreach (var filePath in filePaths)
                {
                    if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                    {
                        continue;
                    }

                    if (_sourceFiles.Any(x => x.Path.Equals(filePath, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    var fileType = SourceFileParamService.GetSupportedFileType(filePath);

                    var sourceFile = new SourceFile
                    {
                        Id = Guid.NewGuid(),
                        Path = filePath,
                        FileName = Path.GetFileName(filePath),
                        FileType = fileType,
                        Status = SourceFileStatus.Pending,
                        AddTime = DateTime.Now
                    };

                    _sourceFiles.Add(sourceFile);
                }

                SyncUserConfig();
            }
        }

        /// <summary>
        /// 获取所有源文件
        /// </summary>
        /// <returns>源文件列表</returns>
        public List<SourceFile> GetAllSourceFiles()
        {
            lock (_lockObject)
            {
                return _sourceFiles.ToList();
            }
        }

        /// <summary>
        /// 根据ID获取源文件
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>源文件，如果不存在则返回null</returns>
        public SourceFile GetSourceFileById(Guid fileId)
        {
            lock (_lockObject)
            {
                return _sourceFiles.FirstOrDefault(f => f.Id == fileId);
            }
        }

        public async Task<SourceFile> ParseSourceFileAsync(Guid fileId)
        {
            var sourceFile = GetSourceFileById(fileId);
            if (sourceFile == null)
            {
                throw new ArgumentException($"找不到ID为 {fileId} 的源文件");
            }

            UpdateFileStatus(fileId, SourceFileStatus.Parsing);

            try
            {
                var parsedParams = await _paramService.ParseSingleFileAsync(sourceFile.Path);

                lock (_lockObject)
                {
                    sourceFile.ParsedParams = parsedParams;
                    sourceFile.Status = SourceFileStatus.Parsed;
                    sourceFile.ErrorMessage = string.Empty;
                }

                return sourceFile;
            }
            catch (Exception ex)
            {
                UpdateFileStatus(fileId, SourceFileStatus.Error, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 更新文件状态
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息（可选）</param>
        /// <returns>更新后的源文件</returns>
        public SourceFile UpdateFileStatus(Guid fileId, SourceFileStatus status, string errorMessage = null)
        {
            lock (_lockObject)
            {
                var sourceFile = _sourceFiles.FirstOrDefault(f => f.Id == fileId);
                if (sourceFile != null)
                {
                    sourceFile.Status = status;
                    if (errorMessage != null)
                    {
                        sourceFile.ErrorMessage = errorMessage;
                    }
                }
                return sourceFile;
            }
        }

        /// <summary>
        /// 移除源文件
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveSourceFile(Guid fileId)
        {
            lock (_lockObject)
            {
                var sourceFile = _sourceFiles.FirstOrDefault(f => f.Id == fileId);
                if (sourceFile != null)
                {
                    _sourceFiles.Remove(sourceFile);
                    SyncUserConfig();
                    return true;
                }
                return false;
            }
        }

        /// <summary>
        /// 清空所有源文件
        /// </summary>
        public void ClearAllSourceFiles()
        {
            lock (_lockObject)
            {
                _sourceFiles.Clear();
                SyncUserConfig();
            }
        }

        /// <summary>
        /// 获取已解析的参数数量
        /// </summary>
        /// <returns>参数数量</returns>
        public int GetParsedParameterCount()
        {
            lock (_lockObject)
            {
                return _sourceFiles
                    .Where(f => f.Status == SourceFileStatus.Parsed)
                    .Sum(f => f.ParsedParams?.Count ?? 0);
            }
        }

        /// <summary>
        /// 获取所有已解析的参数
        /// </summary>
        /// <returns>参数列表</returns>
        public List<ParsedParam> GetAllParsedParameters()
        {
            lock (_lockObject)
            {
                var allParams = new List<ParsedParam>();

                foreach (var file in _sourceFiles.Where(f => f.Status == SourceFileStatus.Parsed))
                {
                    if (file.ParsedParams != null)
                    {
                        allParams.AddRange(file.ParsedParams);
                    }
                }

                return allParams;
            }
        }

        /// <summary>
        /// 根据名称查找参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>匹配的参数列表</returns>
        public List<ParsedParam> FindParametersByName(string parameterName)
        {
            if (string.IsNullOrWhiteSpace(parameterName))
            {
                return new List<ParsedParam>();
            }

            lock (_lockObject)
            {
                return GetAllParsedParameters()
                    .Where(p => p.Name.IndexOf(parameterName, StringComparison.OrdinalIgnoreCase) >= 0)
                    .ToList();
            }
        }
    }
}
