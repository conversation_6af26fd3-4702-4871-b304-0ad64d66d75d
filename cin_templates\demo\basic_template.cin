/*
 * 基础模板示例
 * 包含常用的变量类型
 */

variables
{
  // 基本数据类型
  int gTestMode = 1;
  char gDeviceName[32] = "TestDevice";
  dword gBaudRate = 500000;
  float gTimeout = 5.0;
  
  // 布尔值
  int gEnableLogging = 1;
  int gDebugMode = 0;
  
  // 数组
  int gChannelList[4] = {1, 2, 3, 4};
  char gVersionInfo[16] = "v1.0.0";
}

on start
{
  write("Basic template started");
  write("Test Mode: %d", gTestMode);
  write("Device Name: %s", gDeviceName);
  write("Baud Rate: %d", gBaudRate);
  write("Timeout: %.1f", gTimeout);
  
  if (gEnableLogging)
  {
    write("Logging enabled");
  }
  
  if (gDebugMode)
  {
    write("Debug mode enabled");
  }
}

on key 'q'
{
  write("Quit requested");
  stop();
}
