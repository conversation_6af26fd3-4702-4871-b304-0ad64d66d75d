using Alsi.Common.Parsers.Cin;
using Alsi.Common.Parsers.Cin.Models;
using System.Linq;
using System.Text.RegularExpressions;

namespace Alsi.Tab.Kit.Core.Services.Capl
{
    public static class CaplUtils
    {
        public static string ReplaceVariableValue(string caplContent, string variableName, string newValue)
        {
            if (string.IsNullOrWhiteSpace(caplContent))
            {
                return string.Empty;
            }

            // 解析文件以获取变量信息
            var parseResult = CinParser.ParseContent(caplContent);
            if (!parseResult.Success)
                return null;

            // 找到目标变量
            CinVariable targetVariable = null;
            CinCodeBlock variablesBlock = null;

            foreach (var block in parseResult.CodeBlocks.Where(b => b.ContentType == CinContentType.Variables))
            {
                targetVariable = block.ParsedVariables.FirstOrDefault(v => v.Name == variableName);
                if (targetVariable != null)
                {
                    variablesBlock = block;
                    break;
                }
            }

            if (targetVariable == null)
            {
                return caplContent;
            }

            // 执行替换
            return ReplaceVariableValueInContent(caplContent, targetVariable, newValue);
        }

        /// <summary>
        /// 在内容中替换变量值
        /// </summary>
        private static string ReplaceVariableValueInContent(string content, CinVariable variable, string newValue)
        {
            // 构建变量声明的正则表达式模式
            string pattern = BuildVariablePattern(variable);

            if (string.IsNullOrEmpty(pattern))
                return null;

            // 构建替换字符串
            string replacement = BuildReplacementString(variable, newValue);

            // 执行替换
            var result = Regex.Replace(content, pattern, replacement, RegexOptions.Singleline | RegexOptions.IgnoreCase);
            return result;
        }

        /// <summary>
        /// 构建变量匹配模式
        /// </summary>
        private static string BuildVariablePattern(CinVariable variable)
        {
            string constPrefix = variable.IsConst ? @"const\s+" : "";
            string namePattern = Regex.Escape(variable.Name);

            if (variable.IsArray)
            {
                if (variable.Type.StartsWith("struct"))
                {
                    // struct TypeName varName[size] = { ... };
                    string structType = variable.Type.Substring(7); // 移除"struct "
                    return $@"({constPrefix}struct\s+{Regex.Escape(structType)}\s+{namePattern}\s*\[[^\]]+\]\s*=\s*)([^;]+)(;)";
                }
                else
                {
                    // type varName[size] = { ... };
                    return $@"({constPrefix}{Regex.Escape(variable.Type)}\s+{namePattern}\s*\[[^\]]+\]\s*=\s*)([^;]+)(;)";
                }
            }
            else
            {
                // type varName = value;
                return $@"({constPrefix}{Regex.Escape(variable.Type)}\s+{namePattern}\s*=\s*)([^;]+)(;)";
            }
        }

        /// <summary>
        /// 构建替换字符串
        /// </summary>
        private static string BuildReplacementString(CinVariable variable, string newValue)
        {
            // $1 是声明部分，$3 是分号，我们只替换中间的值部分
            return "${1}" + newValue + "${3}";
        }
    }
}
